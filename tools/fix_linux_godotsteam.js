#!/usr/bin/env node

/**
 * Fix Linux GodotSteam Compatibility Script
 * 
 * This script addresses the C++ ABI compatibility issue with GodotSteam on Linux
 * where the library requires GLIBCXX_3.4.29 which may not be available on older systems.
 * 
 * Solutions provided:
 * 1. Download a more compatible GodotSteam version
 * 2. Build GodotSteam from source with static linking
 * 3. Use an AppImage approach for better compatibility
 */

const fs = require('fs-extra');
const path = require('path');
const { spawn } = require('child_process');
const https = require('https');

class GodotSteamFixer {
  constructor() {
    this.projectRoot = path.resolve(__dirname, '..');
    this.godotSteamDir = path.join(this.projectRoot, 'app', 'addons', 'godotsteam');
    this.backupDir = path.join(this.projectRoot, '_backup_godotsteam');
  }

  async run() {
    console.log('🔧 GodotSteam Linux Compatibility Fixer');
    console.log('=====================================\n');

    // Check current library compatibility
    await this.checkCurrentCompatibility();

    console.log('\n📋 Available solutions:');
    console.log('1. Download compatible GodotSteam 3.29 (recommended)');
    console.log('2. Build GodotSteam from source with static linking');
    console.log('3. Show manual fix instructions');
    console.log('4. Exit');

    const choice = await this.promptUser('\nChoose a solution (1-4): ');

    switch (choice) {
      case '1':
        await this.downloadCompatibleVersion();
        break;
      case '2':
        await this.buildFromSource();
        break;
      case '3':
        this.showManualInstructions();
        break;
      case '4':
        console.log('Exiting...');
        break;
      default:
        console.log('Invalid choice. Exiting...');
    }
  }

  async checkCurrentCompatibility() {
    const libPath = path.join(this.godotSteamDir, 'x11', 'libgodotsteam.so');
    
    if (!await fs.pathExists(libPath)) {
      console.log('❌ GodotSteam library not found at:', libPath);
      return;
    }

    console.log('🔍 Checking current GodotSteam library compatibility...');

    try {
      const result = await this.runCommand('objdump', ['-T', libPath]);
      
      if (result.includes('GLIBCXX_3.4.29')) {
        console.log('❌ Current library requires GLIBCXX_3.4.29 (problematic)');
        
        // Check what versions are required
        const versions = result.match(/GLIBCXX_[\d.]+/g);
        if (versions) {
          const uniqueVersions = [...new Set(versions)].sort();
          console.log('   Required GLIBCXX versions:', uniqueVersions.join(', '));
        }
      } else {
        console.log('✅ Current library appears compatible (no GLIBCXX_3.4.29 requirement)');
      }

      // Check system compatibility
      try {
        const systemLibs = await this.runCommand('strings', ['/usr/lib/libstdc++.so.6']);
        const hasRequired = systemLibs.includes('GLIBCXX_3.4.29');
        
        if (hasRequired) {
          console.log('✅ System has GLIBCXX_3.4.29 - library should work');
        } else {
          console.log('❌ System lacks GLIBCXX_3.4.29 - library will fail to load');
        }
      } catch (error) {
        console.log('⚠️  Could not check system GLIBCXX versions');
      }

    } catch (error) {
      console.log('❌ Could not analyze library:', error.message);
    }
  }

  async downloadCompatibleVersion() {
    console.log('\n📥 Downloading compatible GodotSteam version...');
    
    // Backup current version
    await this.backupCurrentVersion();
    
    const downloadUrl = 'https://github.com/GodotSteam/GodotSteam/releases/download/v3.29/godotsteam-3.29-gdnative-plugin.zip';
    const zipPath = path.join(this.projectRoot, 'godotsteam-3.29.zip');
    
    try {
      console.log('Downloading from:', downloadUrl);
      await this.downloadFile(downloadUrl, zipPath);
      
      console.log('📦 Extracting...');
      await this.extractZip(zipPath, this.godotSteamDir);
      
      // Clean up
      await fs.remove(zipPath);
      
      console.log('✅ Successfully updated to GodotSteam 3.29');
      console.log('🔍 Verifying new library...');
      
      await this.checkCurrentCompatibility();
      
    } catch (error) {
      console.log('❌ Failed to download/install:', error.message);
      console.log('🔄 Restoring backup...');
      await this.restoreBackup();
    }
  }

  async buildFromSource() {
    console.log('\n🔨 Building GodotSteam from source with static linking...');
    console.log('This requires build tools (git, scons, gcc, etc.)');
    
    const proceed = await this.promptUser('Continue? (y/N): ');
    if (proceed.toLowerCase() !== 'y') {
      return;
    }

    const buildDir = path.join(this.projectRoot, '_build_godotsteam');
    
    try {
      // Clone repository
      console.log('📥 Cloning GodotSteam repository...');
      await fs.ensureDir(buildDir);
      await this.runCommand('git', ['clone', '--branch', 'godot3', '--depth', '1', 
                                   'https://github.com/GodotSteam/GodotSteam.git', buildDir]);

      // Build with static linking
      console.log('🔨 Building with static linking...');
      const buildCmd = [
        'scons', 'platform=linux', 'target=release',
        'LINKFLAGS=-static-libgcc -static-libstdc++',
        'CXXFLAGS=-static-libgcc -static-libstdc++'
      ];
      
      await this.runCommand(buildCmd[0], buildCmd.slice(1), { cwd: buildDir });
      
      // Copy built library
      const builtLib = path.join(buildDir, 'bin', 'libgodotsteam.linux.release.64.so');
      const targetLib = path.join(this.godotSteamDir, 'x11', 'libgodotsteam.so');
      
      if (await fs.pathExists(builtLib)) {
        await this.backupCurrentVersion();
        await fs.copy(builtLib, targetLib);
        console.log('✅ Successfully built and installed static-linked GodotSteam');
      } else {
        throw new Error('Built library not found');
      }
      
    } catch (error) {
      console.log('❌ Build failed:', error.message);
      console.log('💡 Try the manual instructions instead');
    } finally {
      // Clean up build directory
      if (await fs.pathExists(buildDir)) {
        await fs.remove(buildDir);
      }
    }
  }

  showManualInstructions() {
    console.log('\n📖 Manual Fix Instructions');
    console.log('==========================\n');
    
    console.log('Option 1: Download Compatible Pre-built Version');
    console.log('-----------------------------------------------');
    console.log('1. Go to: https://github.com/GodotSteam/GodotSteam/releases');
    console.log('2. Download "godotsteam-3.29-gdnative-plugin.zip" (or newer 3.x version)');
    console.log('3. Extract and replace the contents of app/addons/godotsteam/');
    console.log('4. Test the build\n');
    
    console.log('Option 2: Build from Source with Static Linking');
    console.log('-----------------------------------------------');
    console.log('1. Install build dependencies:');
    console.log('   sudo apt-get install build-essential scons git');
    console.log('2. Clone GodotSteam:');
    console.log('   git clone --branch godot3 https://github.com/GodotSteam/GodotSteam.git');
    console.log('3. Build with static linking:');
    console.log('   cd GodotSteam');
    console.log('   scons platform=linux target=release LINKFLAGS="-static-libgcc -static-libstdc++"');
    console.log('4. Copy the built library to your project\n');
    
    console.log('Option 3: Use Export Template with Static Linking');
    console.log('-------------------------------------------------');
    console.log('1. Build custom Godot export templates with static linking');
    console.log('2. This ensures all C++ libraries are statically linked');
    console.log('3. More complex but provides best compatibility\n');
    
    console.log('Option 4: AppImage Distribution');
    console.log('-------------------------------');
    console.log('1. Package your game as an AppImage');
    console.log('2. Include all required libraries in the AppImage');
    console.log('3. Provides excellent compatibility across Linux distributions\n');
  }

  async backupCurrentVersion() {
    if (await fs.pathExists(this.godotSteamDir)) {
      console.log('💾 Backing up current GodotSteam...');
      await fs.ensureDir(this.backupDir);
      await fs.copy(this.godotSteamDir, this.backupDir);
    }
  }

  async restoreBackup() {
    if (await fs.pathExists(this.backupDir)) {
      await fs.copy(this.backupDir, this.godotSteamDir);
      await fs.remove(this.backupDir);
    }
  }

  async runCommand(command, args, options = {}) {
    return new Promise((resolve, reject) => {
      const process = spawn(command, args, { 
        stdio: 'pipe',
        ...options 
      });

      let stdout = '';
      let stderr = '';

      process.stdout.on('data', (data) => {
        stdout += data.toString();
      });

      process.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      process.on('close', (code) => {
        if (code === 0) {
          resolve(stdout);
        } else {
          reject(new Error(`Command failed with code ${code}: ${stderr}`));
        }
      });

      process.on('error', reject);
    });
  }

  async downloadFile(url, filePath) {
    return new Promise((resolve, reject) => {
      const file = fs.createWriteStream(filePath);
      
      https.get(url, (response) => {
        if (response.statusCode === 302 || response.statusCode === 301) {
          // Handle redirect
          return this.downloadFile(response.headers.location, filePath)
            .then(resolve)
            .catch(reject);
        }
        
        if (response.statusCode !== 200) {
          reject(new Error(`HTTP ${response.statusCode}`));
          return;
        }
        
        response.pipe(file);
        
        file.on('finish', () => {
          file.close();
          resolve();
        });
        
        file.on('error', reject);
      }).on('error', reject);
    });
  }

  async extractZip(zipPath, targetDir) {
    // Simple unzip using system command
    await this.runCommand('unzip', ['-o', zipPath, '-d', targetDir]);
  }

  async promptUser(question) {
    const readline = require('readline');
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    return new Promise((resolve) => {
      rl.question(question, (answer) => {
        rl.close();
        resolve(answer.trim());
      });
    });
  }
}

// Run the fixer
if (require.main === module) {
  const fixer = new GodotSteamFixer();
  fixer.run().catch(console.error);
}

module.exports = GodotSteamFixer;
