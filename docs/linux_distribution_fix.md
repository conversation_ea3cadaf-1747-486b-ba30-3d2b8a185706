# Linux Distribution Fix - GodotSteam C++ ABI Issue

## Problem Description

Your Linux builds are failing to load with the error:
```
ERROR: Can't open dynamic library: addons/godotsteam/x11/libgodotsteam.so. 
Error: /lib/x86_64-linux-gnu/libstdc++.so.6: version `GLIBCXX_3.4.29' not found
```

This is a **C++ ABI compatibility issue** where the GodotSteam library was compiled against a newer version of libstdc++ than what's available on the target Linux system.

## Root Cause

The current GodotSteam library (version 3.27) requires `GLIBCXX_3.4.29`, which is only available in:
- GCC 11+ / libstdc++11+
- Ubuntu 22.04+
- Debian 12+
- Recent Arch/Fedora versions

Many users still run older distributions that only have `GLIBCXX_3.4.28` or earlier.

## Solutions

### 🚀 Quick Fix (Recommended)

Run the automated fix script:
```bash
node tools/fix_linux_godotsteam.js
```

This script will:
1. Check your current library compatibility
2. Download a more compatible GodotSteam version
3. Backup your current version
4. Verify the fix worked

### 🔧 Manual Solutions

#### Option 1: Download Compatible Version
1. Go to [GodotSteam Releases](https://github.com/GodotSteam/GodotSteam/releases)
2. Download `godotsteam-3.29-gdnative-plugin.zip` (latest 3.x version)
3. Extract and replace `app/addons/godotsteam/` contents
4. Rebuild your Linux export

#### Option 2: Build with Static Linking
```bash
# Install dependencies
sudo apt-get install build-essential scons git

# Clone GodotSteam
git clone --branch godot3 https://github.com/GodotSteam/GodotSteam.git
cd GodotSteam

# Build with static C++ libraries
scons platform=linux target=release LINKFLAGS="-static-libgcc -static-libstdc++"

# Copy the built library
cp bin/libgodotsteam.linux.release.64.so /path/to/your/project/app/addons/godotsteam/x11/libgodotsteam.so
```

#### Option 3: Use Custom Export Templates
Build Godot export templates with static linking enabled. This is more complex but provides the best compatibility.

#### Option 4: AppImage Distribution
Package your game as an AppImage that includes all required libraries.

## Build Script Integration

The build script (`tools/release_helper/lib/build.js`) has been updated to:
- Detect this compatibility issue during Linux builds
- Warn about potential GLIBCXX problems
- Suggest fixes when the issue is detected

## Testing the Fix

After applying any fix:

1. **Check library compatibility:**
   ```bash
   objdump -T app/addons/godotsteam/x11/libgodotsteam.so | grep GLIBCXX
   ```
   Should not show `GLIBCXX_3.4.29` or higher.

2. **Test on target system:**
   ```bash
   ldd _release/linux/current/libgodotsteam.so
   ```
   Should resolve all dependencies without errors.

3. **Run the game:**
   Test on an older Linux distribution (Ubuntu 20.04, Debian 11, etc.)

## Prevention

To prevent this issue in the future:
- Always test Linux builds on older distributions
- Use static linking for C++ libraries when possible
- Consider using older, more compatible library versions
- Set up CI/CD testing on multiple Linux distributions

## Steam Distribution Notes

When uploading to Steam:
- The Linux depot should work on Steam Deck (Arch-based, recent libraries)
- May fail on older Linux systems in Steam's compatibility layer
- Consider providing both static and dynamic versions
- Test with Steam's Linux runtime

## Related Files

- `tools/fix_linux_godotsteam.js` - Automated fix script
- `tools/release_helper/lib/build.js` - Updated build script with compatibility checks
- `app/addons/godotsteam/` - GodotSteam plugin directory
- `_release/linux/current/` - Linux build output directory

## Additional Resources

- [GodotSteam Documentation](https://godotsteam.com/)
- [GodotSteam GitHub Releases](https://github.com/GodotSteam/GodotSteam/releases)
- [Linux ABI Compatibility Guide](https://gcc.gnu.org/onlinedocs/libstdc++/manual/abi.html)
- [Steam Linux Runtime](https://github.com/ValveSoftware/steam-runtime)
