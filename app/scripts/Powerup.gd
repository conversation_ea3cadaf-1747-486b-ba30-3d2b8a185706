extends Area2D

func _ready():
	# debug
	self.visible = false
	#init(Global.PowerupType.EXTRA_SPEED)
	var _c = connect("area_entered",self,"_on_hit")
	# calculate speed
	speed+=randi()%(Config.PowerupMaxSpeed-Config.PowerupMinSpeed)

var speed = Config.PowerupMinSpeed
var wasInit = false
var powerupType = null
var durationModifier = 0
var speedModifier = 0

func _on_hit(area):
	if area.has_method("canGetPowerup"):
		apply(area)
		queue_free()

func init(type):

	if(type>=Global.powerups.size()):
		# we have no such powerup

		return false

	powerupType = type

	if Global.powerups[type]["char"].length()==1:
		$Sprite/letter1.visible = true
		$Sprite/letter2.visible = false

		$Sprite/letter1/Letter.text = Global.powerups[type]["char"]
		$Sprite/letter1/Shadow.text = Global.powerups[type]["char"]
	else:
		$Sprite/letter1.visible = false
		$Sprite/letter2.visible = true

		$Sprite/letter2/Letter.text = Global.powerups[type]["char"]
		$Sprite/letter2/Shadow.text = Global.powerups[type]["char"]

	# SAFETY: Check if animation exists before setting it
	var animation_name = Global.powerups[type]["type"]
	if $Sprite/Block.frames.has_animation(animation_name):
		$Sprite/Block.animation = animation_name
	else:
		print("Warning: Animation '", animation_name, "' not found, using 'negative' fallback")
		$Sprite/Block.animation = "negative"  # Fallback to existing animation
	
	$Sprite/Block.frame = Global.colorToFrame[Global.powerups[type]["color"]]

	var tween = Global.createTween(self)
	tween.interpolate_property(self,"scale",Vector2(0,0),Vector2(1,1),0.5, Tween.TRANS_BOUNCE, Tween.EASE_IN_OUT)
	tween.start()

	self.visible = true
	wasInit = true

func apply(player):


	if not wasInit:
		return false

	# don't pick up if player is dead or just entering the level
	if not Global.GameScene.isPlayerReady():
		return false

	# fix for a bug when player sometimes takes powerup that's left the screen
	if(global_position.y>Global.getPlayerPosition().y+50):
		return false

	# powerup text

	var _put = Global.powerups[powerupType]["text"]
	var _char = Global.powerups[powerupType]["char"]
	var _tts = Global.powerups[powerupType]["tts"]

	# apply powerup
	match powerupType:

		Global.PowerupType.SHIELD:
			Global.playSound(SoundManager.PowerupSound, Global.getPlayerPosition(), -5);
			Global.GameScene.addShield()

		Global.PowerupType.BULLET_HELL:
			Global.playSound(SoundManager.PowerupSound, Global.getPlayerPosition(), -5);
			Global.GameScene.spawnBulletHell()

		Global.PowerupType.WING1:
			Global.playSound(SoundManager.PowerupSound, Global.getPlayerPosition(), -5);
			Global.getPlayer().addWings(Global.PlayerBulletTypes.SINGLE)

		Global.PowerupType.WING2:
			Global.playSound(SoundManager.PowerupSound, Global.getPlayerPosition(), -5);
			Global.getPlayer().addWings(Global.PlayerBulletTypes.STRONG_SINGLE)

		Global.PowerupType.WING3:
			Global.playSound(SoundManager.PowerupSound, Global.getPlayerPosition(), -5);
			Global.getPlayer().addWings(Global.PlayerBulletTypes.HOMING_SINGLE)

		Global.PowerupType.A:
			Global.getPlayer().decreasePowers(false)
			Global.playSound(SoundManager.BuzzerSound, Global.getPlayerPosition(), -10);
			Global.GameScene.addUpgradeModule(_char)
			Global.GameScene.PlayerLuck += 0.3

		Global.PowerupType.B:
			Global.getPlayer().decreasePowers(false)
			Global.playSound(SoundManager.BuzzerSound, Global.getPlayerPosition(), -10);
			Global.GameScene.addUpgradeModule(_char)
			Global.GameScene.PlayerLuck += 0.3

		Global.PowerupType.C:
			Global.getPlayer().decreasePowers(false)
			Global.playSound(SoundManager.BuzzerSound, Global.getPlayerPosition(), -10);
			Global.GameScene.addUpgradeModule(_char)
			Global.GameScene.PlayerLuck += 0.3

		Global.PowerupType.CRISTAL_DOUBLER:
			Global.playSound(SoundManager.PowerupSound, Global.getPlayerPosition(), -5);
			Global.GameScene.money = Global.GameScene.money*2

		Global.PowerupType.LUCK:
			Global.playSound(SoundManager.PowerupSound, Global.getPlayerPosition(), -5);
			Global.GameScene.addPlayerEffect(Global.PlayerEffect.LUCK, Config.TimedEffectBase, self.durationModifier)

		Global.PowerupType.CURSE:
			Global.playSound(SoundManager.BuzzerSound, Global.getPlayerPosition(), -10);

			# select negative effect
			var _rnd = randi()%4

			Global.getPlayer().decreasePowers(false)

			if(Global.OptionsData.controlType==Global.GameControlMode.MOUSE):
				# if mouse control, no sloth mode - makes no sense
				_rnd = randi()%3

			match(_rnd):
				0:
					_put = "Decrease powers!"
				1:
					_put = "Worst weapon!"
					setWeapon(Global.powerups[Global.PowerupType.WEAPON_1]["extra_data"], player)
				2:
					_put = "Weapon disabled!"
					Global.GameScene.addPlayerEffect(Global.PlayerEffect.WEAPON_DISABLED, Config.TimedEffectCurseBase, self.durationModifier)
				3:
					_put = "Sloth Mode!"
					Global.GameScene.addPlayerEffect(Global.PlayerEffect.SLOTH_MODE, Config.TimedEffectCurseBase, self.durationModifier)
			
			# increase player luck slighty
			Global.GameScene.PlayerLuck += 0.5

		Global.PowerupType.MINE_FIELD:
			Global.playSound(SoundManager.PowerupSound, Global.getPlayerPosition(), -5);
			for _i in range(1,5):
				Global.GameScene.getLevelObject().spawnSpaceMine(true);

		Global.PowerupType.CRYSTAL_MAGNET:
			Global.playSound(SoundManager.PowerupSound, Global.getPlayerPosition(), -5);
			Global.GameScene.addPlayerEffect(Global.PlayerEffect.CRYSTAL_MAGNET, Config.TimedEffectBaseMiddle, self.durationModifier)

		Global.PowerupType.TIMED_SUPER_RAPIDFIRE:
			Global.playSound(SoundManager.PowerupSound, Global.getPlayerPosition(), -5);
			Global.GameScene.addPlayerEffect(Global.PlayerEffect.SUPER_RAPIDFIRE, Config.TimedEffectBaseLong, self.durationModifier)

		Global.PowerupType.TIMED_INVINCIBILITY:
			Global.playSound(SoundManager.PowerupSound, Global.getPlayerPosition(), -5);
			Global.GameScene.addPlayerEffect(Global.PlayerEffect.INVINCIBILITY, Config.TimedEffectBaseMiddle, self.durationModifier)

		Global.PowerupType.TIMED_EXPLODE_TO_CRYSTAL:
			Global.playSound(SoundManager.PowerupSound, Global.getPlayerPosition(), -5);
			Global.GameScene.addPlayerEffect(Global.PlayerEffect.CRYSTAL_EXPLOSION, Config.TimedEffectBase, self.durationModifier)

		Global.PowerupType.EXTRA_BULLET:
			Global.playSound(SoundManager.PowerupSound, Global.getPlayerPosition(), -5);
			player.incBullets()

		Global.PowerupType.EXTRA_SPEED:
			Global.playSound(SoundManager.PowerupSound, Global.getPlayerPosition(), -5);
			player.incSpeed()

		Global.PowerupType.AUTOFIRE:
			Global.playSound(SoundManager.PowerupSound, Global.getPlayerPosition(), -5);
			player.setAutoFire()

		Global.PowerupType.WEAPON_1:
			setWeapon(Global.powerups[powerupType]["extra_data"], player)

		Global.PowerupType.WEAPON_2:
			setWeapon(Global.powerups[powerupType]["extra_data"], player)

		Global.PowerupType.WEAPON_3:
			setWeapon(Global.powerups[powerupType]["extra_data"], player)

		Global.PowerupType.WEAPON_4:
			setWeapon(Global.powerups[powerupType]["extra_data"], player)

		Global.PowerupType.WEAPON_5:
			setWeapon(Global.powerups[powerupType]["extra_data"], player)

		Global.PowerupType.WEAPON_6:
			setWeapon(Global.powerups[powerupType]["extra_data"], player)

		Global.PowerupType.WEAPON_7:
			setWeapon(Global.powerups[powerupType]["extra_data"], player)

		Global.PowerupType.WEAPON_8:
			setWeapon(Global.powerups[powerupType]["extra_data"], player)

		Global.PowerupType.WEAPON_9:
			setWeapon(Global.powerups[powerupType]["extra_data"], player)
	
	# show text
	Global.GameScene.spawnBonusLabel(player.position-Vector2(0,32) ,_put,1.5,true,false,0.8);
	Global.playTts(_tts)

func setWeapon(weaponType, player):

	Global.playSound(SoundManager.PowerupSoundWeapon, Global.getPlayerPosition(), -5);

	if(Global.GameScene.PlayerBulletType >= weaponType):
		player.incBullets()
	Global.GameScene.PlayerBulletType = weaponType

func _process(delta):

	if not wasInit:
		return false

	# falling

	var _speed = speed

	if(self.speedModifier>0):
		_speed = self.speedModifier

	var velocity = Vector2(0,1)
	velocity = velocity.normalized() * _speed
	position += velocity * delta

	# check if out of screen
	if Global.isOffScreenBottom(position, 200):
		queue_free()


