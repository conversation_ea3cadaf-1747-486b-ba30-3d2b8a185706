# Test script to validate centralized bullet processing
# This script can be attached to a test scene to verify everything works correctly

extends Node

var test_pool
var test_bullets = []

func _ready():
	print("=== Centralized Bullet Processing Test ===")
	
	# Create a test bullet pool
	test_pool = BulletPool.create_bullet_pool(self)
	
	if not test_pool:
		print("ERROR: Failed to create bullet pool")
		return
	
	print("✓ Bullet pool created successfully")
	
	# Test pool methods exist
	if test_pool.has_method("process_all_bullets"):
		print("✓ process_all_bullets method exists")
	else:
		print("✗ process_all_bullets method missing")
	
	# Create some test bullets
	for i in range(5):
		var bullet = test_pool.get_bullet()
		if bullet:
			bullet.position = Vector2(i * 50, 100)
			test_bullets.append(bullet)
			print("✓ Created test bullet %d at position %s" % [i+1, bullet.position])
		else:
			print("✗ Failed to create test bullet %d" % [i+1])
	
	print("✓ Created %d test bullets" % test_bullets.size())
	print("Pool stats: %s" % str(test_pool.get_stats()))
	
	# Test that individual processing is disabled
	for bullet in test_bullets:
		if bullet.is_processing():
			print("✗ Individual processing still enabled for bullet")
		else:
			print("✓ Individual processing correctly disabled")
		break  # Only check first bullet
	
	print("=== Test Complete ===")

func _process(delta):
	# Test centralized processing
	if test_pool:
		test_pool.process_all_bullets(delta)
	
	# Clean up bullets after a few seconds
	if get_tree().get_frame() > 300:  # About 5 seconds at 60fps
		for bullet in test_bullets:
			if is_instance_valid(bullet):
				bullet.destroy(true)
		test_bullets.clear()
		queue_free()  # Remove test
