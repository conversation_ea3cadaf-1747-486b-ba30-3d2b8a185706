extends Node2D

export var steer_force = 10.0

export var canKillPlayer = true

var homingVelocity = Vector2.ZERO
var acceleration = Vector2.ZERO
var target = null

var steerTimer = 0
var destroyTimer = 0

var lifetimeMsec = 2000
var seekStartMsec = 100

func isDeadly():
	return canKillPlayer

var Speed = Config.EnemyBulletSpeed * 0.5

var isDestroyed = false

var doCountTowardBulletsOnScreen = true

var Sparkle = preload("res://scenes/Sparkle.tscn")

func canSteer():
	return Tick.ms() > steerTimer

func destroy(_force = false):

	if isDestroyed:
		return false
	
	isDestroyed = true

	if(doCountTowardBulletsOnScreen):
		Global.EnemyBulletsOnScreen -= 1
		Global.EnemyBulletsOnScreen = max(0, Global.EnemyBulletsOnScreen)

	var sparkle = Sparkle.instance()
	sparkle.position = position
	Global.GameScene.add_child(sparkle)

	queue_free()

func initStart(startPos, endPos, start_rotation):

	global_transform = Transform2D()
	rotation_degrees = start_rotation+90

	var tween = Global.createTween(self)
	tween.interpolate_property(self,"global_position",startPos,endPos,1.0, Tween.TRANS_QUAD, Tween.EASE_OUT)
	tween.connect("tween_all_completed",self,"start",[start_rotation, endPos])
	tween.start()

var didStart = false

func start(start_rotation, startPos):
	didStart = true
	
	global_position = startPos
	steer_force = steer_force+(randi()%15)

	steerTimer = Tick.ms() + seekStartMsec
	destroyTimer = Tick.ms() + lifetimeMsec

	rotation_degrees = start_rotation+90
	homingVelocity = transform.x * Speed
	target = Global.getPlayer()

func seek():
	var steer = Vector2.ZERO

	if is_instance_valid(target) and canSteer():
		var desired = (target.global_position - position).normalized() * Speed
		steer = (desired - homingVelocity).normalized() * steer_force

	return steer

func checkDestroy():
	if isDestroyed:
		return

	if Tick.ms() > destroyTimer:
		steer_force = 0
	
	if Global.isOffScreen(global_position,10):
		self.destroy()

func _process(_delta):
	checkDestroy()

func _on_hit(area):
	if(area.has_method("getDamagePoint")):
		Global.callIfExists(area,"destroy")

func _ready():
	var _c = connect("area_entered",self,"_on_hit")

	pass

func _physics_process(delta):

	if(!didStart):
		return false

	if(Global.isOffScreenBottom(global_position, -200)):
		$Particles2D.visible = false
		acceleration = Vector2.ZERO
	else:
		acceleration += seek()

	homingVelocity += acceleration * delta
	homingVelocity = homingVelocity.clamped(Speed)
	rotation = homingVelocity.angle()
	position += homingVelocity * delta
