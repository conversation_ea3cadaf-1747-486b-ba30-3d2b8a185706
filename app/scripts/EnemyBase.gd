extends Area2D

var species = "enemy_base"

# starting point on grid
var entryDestination = Vector2(-100,-100)

# position in row
var rowPosition = -1

# positino in col
var colPosition = -1

# count in the scene
var count = 0

# properties

# we save max health here
var initialHealth = -1

# enemy health
var health = 1

# enemy health
var pointValue = 1000

# enemy mode
var mode = -1

var canKillPlayer = true

func isDeadly():
	return canKillPlayer && self.mode != Global.EnemyMode.DEAD

var HitColor = Color(1,1,1)
var ExplosionColor = Color(1,1,1,1)

var wiggleMode = -1 # default

var wasCollisionKill = false

# enemy attacks only one time, does not come back
var shouldDieAfterAttack = false

# if agressive it will fire on it's own independently from coordinator
var isAgressive = false

# agression multiplier ( for shooting )
# the smaller the more frequest the bullets will be fired
var agressionMultiplier = 1.0


var levelType = Global.LevelType.NORMAL

# Commented out to avoid unused signal warning - can be re-enabled if needed
# signal enemy_state_changed(ogject, previousState, state)

var Explosion = preload("res://scenes/Explosion_1.tscn")
var Bullet = preload("res://scenes/EnemyBullet_1.tscn")

# used to identify it
func isEnemy():
	return true

func changeState(state):

	if(self.mode != state):

		var previousState = self.mode

		if(state == Global.EnemyMode.ATTACK):
			Global.EnemiesAttacking+=1
		
		if(self.mode == Global.EnemyMode.ATTACK):
			Global.EnemiesAttacking-=1
			Global.EnemiesAttacking = max(0, Global.EnemiesAttacking)

		self.mode = state

		# Commented out to match commented signal above
		# call_deferred("emit_signal","enemy_state_changed",self,previousState, state)

var flashShader = preload("res://shaders/hit.tres")

func getSprite():
	return get_node("AnimatedSprite")

var agressionTimeBase = 0

func _ready():
	var _c = connect("area_entered",self,"_on_hit")
	self.getSprite().material = ShaderMaterial.new()
	self.getSprite().material.shader = flashShader
	agressionTimeBase = randi()%10*100

func tweenEnd():
	if levelType == Global.LevelType.BONUS:
		die(true)
	else:
		changeState(Global.EnemyMode.IDLE)

func returnAfterAttack():

	# enemy should die after attack
	if (self.shouldDieAfterAttack):
		die(true)
		return 0;

	# return to top
	var tween = Global.createTween(self)
	changeState(Global.EnemyMode.ENTRY)
	tween.interpolate_property(self,"position",position,self.entryDestination, 2.0+(0.3*rowPosition),Tween.TRANS_LINEAR, Tween.EASE_OUT);
	tween.connect("tween_all_completed",self,"tweenEnd")
	tween.start()

func getEntryDelay():

	if Global.GameScene.levelConductor.getCurrent().getEnemyEntryDelay() == []:
		return rowPosition*2

	return Global.GameScene.levelConductor.getCurrent().getEnemyEntryDelay()[rowPosition]*1.5

func incHealthDiff():
	var dict = {
		Global.GameDifficulty.EASY: 0,
		Global.GameDifficulty.NORMAL: 0,
		Global.GameDifficulty.HARD: 20,
		Global.GameDifficulty.EXTREME: 30
	}

	var _h = self.health+dict[Global.GameScene.difficulty]

	if(Global.GameScene.isMode(Global.GameMode.FLOW)):
		_h+=10

	return _h

func init(lvlType = Global.LevelType.NORMAL):

	# set health based on difficulty
	self.health = incHealthDiff()

	#init

	levelType = lvlType
	# delay the tween for each stuff
	changeState(Global.EnemyMode.INIT)
	var rowDelay = Config.EnemyRowDelay 

	if levelType == Global.LevelType.BONUS:
		rowDelay = Config.EnemyRowDelayBonus

	var initialDelay = Config.EnemyInitialDelay

	if levelType == Global.LevelType.BONUS:
		initialDelay = Config.EnemyInitialDelayBonus

	# move it away before start the animation
	global_position.x = -100000;
	global_position.y = -100000;

	Global.setTimeout(self, initialDelay + (getEntryDelay()*Global.GameScene.getEnemyEntrySpeed()*rowDelay) + (Global.GameScene.getEnemyEntrySpeed()*colPosition*Config.EnemyColDelay),self,"enterScreen")


func enterScreen():
	changeState(Global.EnemyMode.ENTRY)

func wigglePosition():

	var _mod = 1.0

	if wiggleMode == Global.WiggleMode.COL:
		_mod = sin(colPosition+1)

	if wiggleMode == Global.WiggleMode.ROW:
		_mod = sin(rowPosition+1)

	if wiggleMode == Global.WiggleMode.COL_ROW:
		_mod = sin(colPosition+rowPosition+1)

	if wiggleMode == Global.WiggleMode.COUNT:
		_mod = sin(count)

	return entryDestination.x + Global.GameScene.wigglePosition * _mod + ((randf()*Config.WiggleShake*2)-Config.WiggleShake)

var attackWiggleMultiplier = -1.5

var lastAttackVelocity = 0

func preProcess(_delta):
	return true

var msec_offset = 0;

var pathAnimator = PathAnimator.new() 
var pathData = {}

func setPathData(pData, startDestination:Vector2, isNormalStyleLelve):
	var finalCoord = null

	if(isNormalStyleLelve):
		finalCoord = startDestination

	pathAnimator.setPathData(pData, finalCoord, 5, Vector2(20,20))
	pathData = pData

var previousPosition = Vector2(0,0)

func calcRotationDegrees(delta):

	if(mode == Global.EnemyMode.ATTACK):
		return 0

	var coords = Vector2(0,0)
	var prevPos = Vector2(0,0)

	if(global_position):
		coords = global_position

	if(previousPosition):
		prevPos = previousPosition
	
	var diff = prevPos.x - coords.x

	var speed = (diff/delta)/10

	if(speed==0):
		return 0
	
	var prefix = -(speed/abs(speed))

	return prefix*min(20,abs(speed))

	# var prefix = 1

	# if(diff<0):
	# 	prefix = -1
	
	# var r = diff*diff*delta*25

	# r = min(25,r)

	# return -prefix*r

func rotationHandler(delta):
	rotation_degrees = calcRotationDegrees(delta)

func attackSpeedMultiplier():
	return 1.0


func _process(delta):

	# set max health
	if(initialHealth<0):
		initialHealth = health

	var _percentage = Global.getPercentage(max(health,1),max(initialHealth,1));
	
	if(is_instance_valid(get_node("AnimatedSprite"))):
		get_node("AnimatedSprite").speed_scale = 1+(5-(5*(_percentage/100)))

	#wiggle if iddle

	rotationHandler(delta)
	previousPosition = global_position

	if(!preProcess(delta)):
		return false

	if(isAgressive):
		if(!Global.doThrottle("enemy_agression_throttle"+str(self.get_instance_id()), ((agressionTimeBase+1500)*agressionMultiplier)+randi()%500)):
			if(Global.isChance(randi(), Config.EnemyBulletChance_AttackingBase)):
				self.fireBullet()

	if(self.mode == Global.EnemyMode.ENTRY):
		if(pathAnimator && !pathAnimator.didEnd()):
			pathAnimator.move(delta)
			global_position = pathAnimator.getPosition()
		else:
			tweenEnd()
			pathAnimator = null


	if(self.mode == Global.EnemyMode.IDLE):
		var diff = abs(position.x - wigglePosition());
		if(position.x < wigglePosition()):
			position.x += diff/2*delta
		else:
			position.x -= diff/2*delta
	
	if(self.mode == Global.EnemyMode.ATTACK):
		var sinpart = 1+sin(float((Tick.ms()+msec_offset)+1000*attackRandomizer)/1000)
		sinpart = max(0.8,sinpart)
		sinpart = min(1.3,sinpart)

		if(isAgressive):
			sinpart*=1.5

		var yvelocity = Config.EnemyAttackSpeed*sinpart*attackSpeedMultiplier()

		if(!Global.GameScene.isPlayerReady()):
			yvelocity*=3

		position.y += (yvelocity*delta)

		var velocity = 0;

		var attackWiggle = Global.GameScene.wigglePosition*attackWiggleMultiplier

		if(isAgressive):
			attackWiggle*=(randi()%2+2)
		
		var destination = Global.getPlayerPosition().x-attackWiggle

		var _diff = abs(position.x - destination)

		if position.x < destination:
			velocity = _diff/200
		else:
			velocity = -(_diff/200)
		
		# keep velocity if lower than N pixe from bottom
		if(position.y>=Global.getWindowSize().y-150):
			velocity = lastAttackVelocity
		else:
			lastAttackVelocity = velocity
		
		position.x += (velocity*Config.EnemyAttackSpeed*delta)

		if Global.isOffScreenBottom(position, 100):
			position = Vector2(Global.getWindowSize().x/2, -(100+randi()%200))
			returnAfterAttack()
	

var attackRandomizer = 0.5

func startAttack():
	if(self.mode == Global.EnemyMode.IDLE):
		attackWiggleMultiplier = 1 + (randf()*3 - 1.5)
		if(randf()>0.5):
			attackWiggleMultiplier = -attackWiggleMultiplier

		z_index = Config.TopZIndex
		attackRandomizer = randf()
		changeState(Global.EnemyMode.ATTACK)
		Global.playSound(SoundManager.AttackSound, position, -10)

func getId(prefix = "", suffix = ""):
	return prefix+"Enemy_"+str(count)+suffix

func afterBulletFired(_bullet):
	pass

func fireBullet(doForce = false):

	if(!doForce):
		if (Global.doThrottle(getId("",".Bullet") ,Config.GlobalEnemyBulletThrottle) || !Global.isEnemyActive(mode)):
			return false

	# don't shoot if too close to bottom
	if Global.isOffScreenBottom(getCenterPosition(), -200):
		return false

	var bullet = Bullet.instance()
	bullet.position = getCenterPosition();
	bullet.z_index  = z_index-1;
	Global.GameScene.add_child(bullet);

	Global.EnemyBulletsOnScreen += 1

	afterBulletFired(bullet)


func _fon():
	self.getSprite().material.set_shader_param("active", 1)
	self.getSprite().material.set_shader_param("flash_color", self.HitColor)

func _foff():
	self.getSprite().material.set_shader_param("active", 0)

func flash():
	Global.playSound(SoundManager.HitSound, position, -10)

	_fon()
	Global.setTimeout(self,0.1,self,"_foff")

	# var tween = Global.createTween(self)
	# tween.interpolate_property(self,"modulate",Color(1000,1000,1000),Color(1,1,1),1.0, Tween.TRANS_LINEAR, Tween.EASE_OUT);
	# tween.start()

func canBeDamaged():
	return true

func preDamage():
	return true

func causeDamage(points, doSpawnCrystal = false):

	preDamage()

	if(!self.canBeDamaged()):
		return false

	self.health-=points
	checkHealth(doSpawnCrystal)

func removeFromScene():
	self.queue_free()

func getCenterPosition():
	return global_position

func beforeDie():
	return true

func die(removeOnly = false):

	if(!beforeDie()):
		return false

	if(self.mode == Global.EnemyMode.DEAD):
		return false

	if(!removeOnly):
		# add stats
		Global.GameScene.levelConductor.addEnemyKillLog(self)

	changeState(Global.EnemyMode.DEAD)

	if not removeOnly:
		# play explosion
		var explosion = Explosion.instance()
		explosion.position = getCenterPosition();
		explosion.z_index  = z_index+1;
		explosion.explosionColorMod = ExplosionColor
		Global.GameScene.add_child(explosion);

		var tween = Global.createTween(self)
		tween.interpolate_property(self,"scale",Vector2(1,1),Vector2(0,0),0.2, Tween.TRANS_EXPO, Tween.EASE_OUT)
		tween.interpolate_property(self,"modulate:a",1,0,0.2, Tween.TRANS_QUAD, Tween.EASE_OUT)
		tween.connect("tween_all_completed",self,"removeFromScene")
		tween.start()

		Global.GameScene.call_deferred("handleLoot",self.global_position)
		
	else:
		removeFromScene()


func checkHealth(doSpawnCrystal = false):
	if(self.health <= 0):
		# give points to the user
		Global.GameScene.addScore(pointValue, position)
		if(doSpawnCrystal && self.mode != Global.EnemyMode.DEAD):
			Global.GameScene.spawnCrystal(global_position,Global.CrystalType.c50);
		die()
	else:
		#taking damage
		flash()

var lastBullet = null

func preHit(_target):
	pass

func throttleDamage():
	return false

func _on_hit(target):

	# no hit if enemy is off screen
	if Global.isOffScreenBottom(position, 50) || Global.isOffScreenTop(position,30):
		return false
	
	# make sure that one bullet can only hit once
	if lastBullet == target:
		return false

	lastBullet = target

	preHit(target)

	if(target.has_method("getDamagePoint") && self.mode != Global.EnemyMode.DEAD):
		# cause damage
		if(!throttleDamage()):
			causeDamage(target.getDamagePoint(), target.has_method("isAreaExplosion"))
		# destroy bullet
		Global.callIfExists(target,"destroy", true)
