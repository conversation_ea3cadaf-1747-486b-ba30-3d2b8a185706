# Pool Configuration
# Centralized settings for all object pools in the game
# Adjust these values to optimize performance vs memory usage

extends Node

class_name PoolConfig

# === BULLET POOL CONFIGURATION ===

# Basic bullet pool settings
const BULLET_POOL_INITIAL = 200
const BULLET_POOL_MAX = 200
const BULLET_POOL_MAX_INACTIVE = 150

# Super bullet pool settings
const BULLET_SUPER_POOL_INITIAL = 150
const BULLET_SUPER_POOL_MAX = 150
const BULLET_SUPER_POOL_MAX_INACTIVE = 100

# Homing bullet pool settings
const BULLET_HOMING_POOL_INITIAL = 250
const BULLET_HOMING_POOL_MAX = 250
const BULLET_HOMING_POOL_MAX_INACTIVE = 150

# Laser bullet pool settings
const BULLET_LASER_POOL_INITIAL = 10
const BULLET_LASER_POOL_MAX = 50
const BULLET_LASER_POOL_MAX_INACTIVE = 15

# === CRYSTAL POOL CONFIGURATION ===

# Crystal pool settings
const CRYSTAL_POOL_INITIAL = 100
const CRYSTAL_POOL_MAX = 100
const CRYSTAL_POOL_MAX_INACTIVE = 50

# === SMART CLEANUP CONFIGURATION ===

# Cleanup timing (frames between cleanup checks)
const CLEANUP_CHECK_FRAMES = 300  # Every 5 seconds at 60fps

# Cleanup threshold (cleanup when active objects < this ratio of inactive objects)
const CLEANUP_THRESHOLD_RATIO = 0.2  # 20% - cleanup if active < 20% of inactive

# === PERFORMANCE NOTES ===

# Pool Size Guidelines:
# - Initial size: Pre-allocated objects to avoid allocation during gameplay
# - Max size: Hard limit to prevent unlimited growth (objects created beyond this still work but aren't pooled)
# - Max inactive size: Soft limit for cleanup - excess inactive objects are removed periodically
#
# Memory vs Performance Trade-offs:
# - Higher initial/max sizes = less allocation overhead, more memory usage
# - Lower max_inactive sizes = less memory usage during low activity, more frequent cleanup
# - Higher cleanup threshold ratios = less aggressive cleanup, more memory usage
#
# Recommended Settings by Game Type:
# - Bullet Hell Games: Higher initial sizes, more aggressive cleanup
# - Casual Games: Lower initial sizes, less aggressive cleanup
# - Mobile Games: Lower max sizes, more aggressive cleanup

# === DEBUGGING ===

# Enable/disable cleanup debug messages
const DEBUG_CLEANUP = true

# Pool statistics collection (for debugging/profiling)
const COLLECT_STATS = true
