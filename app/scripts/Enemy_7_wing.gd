extends "res://scripts/EnemyBase.gd"

var fallSpeed = 100
var rotDegrees = 10

func _ready():
	Bullet = preload("res://scenes/EnemyBullet_1.tscn")
	health = 60
	ExplosionColor = Color(0.2,0.2,1)
	species = "Wing"
	fallSpeed = 100+randi()%100
	rotDegrees -= randi()%20
	mode = Global.EnemyMode.IDLE
	pointValue = 6000
	setScratch(100.0)

export var isLeft = false

func throttleDamage():

	var _percentage = Global.getPercentage(health,initialHealth);
	self.setScratch(_percentage)

	return Global.doThrottle("Enemy7WingThrottle" + str(get_instance_id()), Config.BossDamageThrottleShort)

func preHit(target):
	if(!Global.doThrottle("wingCrystalThrottle",200)):
		if(target.has_method("getDamagePoint") && self.mode == Global.EnemyMode.DEAD):
			flash()
			Global.callIfExists(target,"destroy", true)
			Global.GameScene.spawnCrystal(target.global_position,randi()%3);


func wigglePosition():
	return 0

const WOffset = 48 

func setScratch(_percentage):
	if is_instance_valid($"Scratch"):
		$"Scratch".modulate.a  =(100-_percentage)/100.0

func getCenterPosition():
	var p = global_position

	if(isLeft):
		p.x = p.x - WOffset
	else:
		p.x = p.x + WOffset

	return p

func isEnemyWing():
	return true

func hideAnim():
	var tween = Global.createTween(self)
	tween.interpolate_property(self,"scale",Vector2(1,1),Vector2(0,0),0.5, Tween.TRANS_QUAD, Tween.EASE_OUT)
	tween.interpolate_property(self,"modulate:a",1,0,0.5, Tween.TRANS_QUAD, Tween.EASE_OUT)
	tween.start()
	Global.setTimeout(self,0.5,self,"queue_free")

func fireBullet(doForce = false):

	if(!Global.isEnemyActive(mode)):
		return false

	if(!doForce):
		if (Global.doThrottle(getId("",".Bullet") ,Config.GlobalEnemyBulletThrottle) || !Global.isEnemyActive(mode)):
			return false

	# don't shoot if too close to bottom
	if Global.isOffScreenBottom(position, -200):
		return false

	var bullet = Bullet.instance()
	bullet.position = getCenterPosition();
	bullet.z_index  = z_index-1;
	bullet.velocity.x=-0.1

	var bullet2 = Bullet.instance()
	bullet2.position = getCenterPosition();
	bullet2.z_index  = z_index-1;
	bullet2.velocity.x=0.1

	Global.GameScene.add_child(bullet2);
	Global.GameScene.add_child(bullet);

	Global.EnemyBulletsOnScreen += 1

func preProcess(delta):

	if(self.mode != Global.EnemyMode.DEAD):
		return true
	
	var velocity = Vector2(0,1)
	velocity = velocity.normalized() * fallSpeed
	position += velocity * delta

	# check if out of screen
	if Global.isOffScreenBottom(position, 200):
		call_deferred("queue_free")
	
	return false

func rotationHandler(_delta):
	rotation_degrees = 0# calcRotationDegrees(_delta)/10

func die(removeOnly = false):

	$Particles2D.emitting = true

	if(self.mode == Global.EnemyMode.DEAD):
		return false

	if(!removeOnly):
		# add stats
		Global.GameScene.levelConductor.addEnemyKillLog(self)
	
	var _coord = global_position
	
	# CRITICAL FIX: Notify parent Enemy_7 that wing is being destroyed
	var parent = get_parent()
	if parent and parent.has_method("_on_wing_destroyed"):
		parent.call_deferred("_on_wing_destroyed", self.name)

	self.get_parent().call_deferred("remove_child",self)
	Global.GameScene.add_child_deferred(self)

	set_deferred("position",_coord)
	set_deferred("global_position",_coord)

	get_node("AnimatedSprite").frame = 2

	var explosion = Explosion.instance()
	explosion.position = getCenterPosition();
	explosion.z_index  = z_index+1;
	explosion.explosionColorMod = ExplosionColor
	Global.GameScene.add_child(explosion);

	changeState(Global.EnemyMode.DEAD)

	Global.GameScene.spawnBonusCrystals(5,false,getCenterPosition(),true)
