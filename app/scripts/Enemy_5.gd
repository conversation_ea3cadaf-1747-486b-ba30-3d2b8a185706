extends "res://scripts/EnemyBase.gd"


func _ready():
	Bullet = preload("res://scenes/EnemyBullet_4.tscn")
	health = 70
	ExplosionColor = Color(0.2,0.2,1)
	species = "Blade"
	pointValue = 3000

func fireBullet(doForce = false):

	if(!doForce):
		if (Global.doThrottle(getId("",".Bullet") ,Config.GlobalEnemyBulletThrottle) || !Global.isEnemyActive(mode)):
			return false

	var bullet = Bullet.instance()
	bullet.position = getCenterPosition();
	bullet.z_index  = z_index-1;
	bullet.velocity.y= 0.9+randf()*0.2
	bullet.randomJiggle = false;
	bullet.doJiggle = true;

	Global.GameScene.add_child(bullet);

	Global.EnemyBulletsOnScreen += 1
