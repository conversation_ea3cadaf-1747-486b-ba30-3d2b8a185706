extends Area2D

var canKillPlayer = true

var flashShader = preload("res://shaders/hit.tres")

var HitColor = Color(1,1,1)
var Explosion = preload("res://scenes/Explosion_1.tscn")

func getSprite():
	return get_node("AnimatedSprite")

var hitCounter = 0
var ExplosionColor = Color(1,1,1,1)

func getPossiblePowerups():
	return [
		Global.PowerupType.WING1,
		Global.PowerupType.WING2,
		Global.PowerupType.WING3,
		Global.PowerupType.TIMED_SUPER_RAPIDFIRE,
		Global.PowerupType.SHIELD
	]

func isDeadly():
	return canKillPlayer

func getRandomIndex():
	var _pwui = randi()%getPossiblePowerups().size()
	return _pwui

func spawnPowerup():
	var radius = 50
	for _i in range(0,3):
		Global.GameScene.spawnPowerup(Vector2(-radius,-radius)+global_position+Vector2(randi()%(2*radius),randi()%(2*radius)), getPossiblePowerups()[getRandomIndex()]);

func init():
	Global.GameScene.powerupShipCount+=1
	Global.GameScene.displayNotification("Powerup ship ahead!","Alert")
	global_position.y = -200

func _ready():
	var _c = connect("area_entered",self,"_on_hit")
	self.getSprite().material = ShaderMaterial.new()
	self.getSprite().material.shader = flashShader

	global_position.x = 3*(Global.getWindowSize().x/4) - (randf()*Global.getWindowSize().x/2)

func _fon():
	self.getSprite().material.set_shader_param("active", 1)
	self.getSprite().material.set_shader_param("flash_color", self.HitColor)

func _foff():
	self.getSprite().material.set_shader_param("active", 0)

func flash():
	Global.playSound(SoundManager.HitSound, position, -10)

	_fon()
	Global.setTimeout(self,0.1,self,"_foff")

var lastBullet = null

var pointValue = 100000

func getCenterPosition():
	return global_position

var isDead = false

func _removeFromScene():

	if(hitCounter==0):
		Achievments.acquire("spared_powerup_ship")

	call_deferred("queue_free")
	Global.GameScene.powerupShipCount-=1

func die(removeOnly = false):

	if(isDead):
		return false
	
	if !removeOnly:

		# play explosion
		var explosion = Explosion.instance()
		explosion.position = getCenterPosition();
		explosion.z_index  = z_index+1;
		explosion.explosionColorMod = ExplosionColor
		Global.GameScene.add_child(explosion);

		var tween = Global.createTween(self)
		tween.interpolate_property(self,"scale",Vector2(1,1),Vector2(0,0),0.2, Tween.TRANS_EXPO, Tween.EASE_OUT)
		tween.interpolate_property(self,"modulate:a",1,0,0.2, Tween.TRANS_QUAD, Tween.EASE_OUT)
		tween.connect("tween_all_completed",self,"removeFromSceneAndSpawnPowerup")
		tween.start()

	else:
		_removeFromScene()

	isDead = true

func removeFromSceneAndSpawnPowerup():
	spawnPowerup()
	_removeFromScene()

func checkHealth():
	if(self.hitCounter>=Config.PowerupShipHits):
		# give points to the user
		Achievments.acquire("destroyed_powerup_ship")
		Global.GameScene.addScore(pointValue, position)
		die()
	else:
		#taking damage
		flash()

func causeDamage():

	if Global.doThrottle("powerupShipHit", 100):
		return false

	hitCounter+=1
	checkHealth()

func _on_hit(target):

	# no hit if enemy is off screen
	if Global.isOffScreenBottom(position, 50):
		return false

	# make sure that one bullet can only hit once
	if lastBullet == target:
		return false

	lastBullet = target

	if(target.has_method("getDamagePoint")):
		# cause damage
		causeDamage()
		# destroy bullet
		Global.callIfExists(target,"destroy", true)

var velocity = Vector2(0,1)
var Speed = Config.PowerupShipSpeed
var _speedMod = 1.0

func getSpeedMod():
	return self._speedMod

func _process(delta):

	var _velocity = velocity*Speed; #velocity.normalized() * Speed
	_velocity.x = velocity.x * Speed
	position += _velocity * delta * Vector2(1, getSpeedMod())

	# destroy bullet if it goes off screen
	if position.y > Global.getWindowSize().y+100:
		_removeFromScene()

	if position.x > (Global.getWindowSize().x+100):
		_removeFromScene()

	if position.x < -100:
		_removeFromScene()
