# Generic Bullet Pool for all bullet types
# Provides optimized bullet management with proper state reset

extends Node

class_name BulletPool

# Pool configuration
var scene_resource
var parent_node
var available_objects = []
var active_objects = []
var max_pool_size = 150  # Set by initialize()
var initial_pool_size = 20  # Set by initialize()

# Smart cleanup configuration (uses PoolConfig values)
var max_inactive_size = PoolConfig.BULLET_POOL_MAX_INACTIVE
var cleanup_check_frames = PoolConfig.CLEANUP_CHECK_FRAMES
var cleanup_frame_counter = 0
var cleanup_threshold_ratio = PoolConfig.CLEANUP_THRESHOLD_RATIO

# Debug statistics configuration
var debug_stats_frames = 60  # Output stats every second at 60fps
var debug_stats_counter = 0
var pool_type_name = "Bullet"  # For debug output identification

# Initialize the bullet pool
func initialize(scene, parent, initial_size = 20, max_size = 150):
	scene_resource = scene
	parent_node = parent
	initial_pool_size = initial_size
	max_pool_size = max_size

	# Pre-populate the pool
	for _i in range(initial_pool_size):
		var obj = scene_resource.instance()
		obj.set_process(false)
		obj.set_physics_process(false)
		obj.visible = false
		available_objects.append(obj)

# Get an object from the pool
func get_object():
	var obj

	if available_objects.size() > 0:
		# Reuse an existing object
		obj = available_objects.pop_back()
	else:
		# Create a new object if pool is empty and we haven't hit the limit
		if active_objects.size() < max_pool_size:
			obj = scene_resource.instance()
		else:
			# Pool is at capacity, return null or oldest active object
			# print("Warning: BulletPool at capacity, creating new object anyway")
			obj = scene_resource.instance()

	# Activate the object (but disable individual processing - centralized now)
	obj.set_process(false)  # Individual processing disabled - handled by pool
	obj.set_physics_process(false)  # Individual physics processing disabled - handled by pool
	obj.visible = true

	# Add to parent only if it doesn't already have one
	if not obj.get_parent():
		parent_node.add_child(obj)
	active_objects.append(obj)

	return obj

# Return an object to the pool
func return_object(obj):
	if obj == null or not is_instance_valid(obj):
		return

	# Remove from active tracking
	var index = active_objects.find(obj)
	if index >= 0:
		active_objects.remove(index)

	# Remove from scene tree
	if obj.get_parent():
		obj.get_parent().remove_child(obj)

	# Reset object state
	_reset_object(obj)

	# Deactivate the object
	obj.set_process(false)
	obj.set_physics_process(false)
	obj.visible = false

	# Return to available pool if we have space
	if available_objects.size() < max_pool_size:
		available_objects.append(obj)
	else:
		# Pool is full, destroy the object
		obj.queue_free()

# Reset method to properly reset bullet objects
func _reset_object(obj):
	if obj.has_method("reset_for_pool"):
		obj.reset_for_pool()
	else:
		# Fallback reset based on bullet type
		if obj.get_script() and obj.get_script().get_path().ends_with("BulletHoming.gd"):
			_reset_bullet_homing(obj)
		elif obj.get_script() and obj.get_script().get_path().ends_with("BulletLaser.gd"):
			_reset_bullet_laser(obj)
		else:
			# Default reset for basic bullets (Bullet.gd, BulletSuper.gd)
			_reset_bullet_base(obj)

# Specialized reset for BulletHoming objects
func _reset_bullet_homing(bullet):
	# IMPORTANT: Clear old throttle key from Global dictionary to prevent memory leak
	if bullet.has("cached_throttle_key") and bullet.cached_throttle_key != "" and Global.throttleDic.has(bullet.cached_throttle_key):
		Global.throttleDic.erase(bullet.cached_throttle_key)

	# Reset position and transform
	bullet.position = Vector2.ZERO
	bullet.rotation = 0
	bullet.scale = Vector2.ONE
	bullet.modulate = Color(1, 1, 1, 1)

	# Reset bullet state variables
	bullet.isDestroyed = false
	bullet.wasBulletRemoved = false
	bullet.doCountTowardBulletsOnScreen = true
	bullet.isPassThrough = false
	bullet.canClash = false

	# Reset movement variables
	bullet.velocity = Vector2.ZERO
	bullet.acceleration = Vector2.ZERO
	bullet.target = null

	# Reset timers and counters
	bullet.steerTimer = 0
	bullet.destroyTimer = 0
	bullet.current_time_cache = 0
	bullet.target_selection_counter = 0
	bullet.destroy_check_counter = 0
	bullet.bullet_count_timer = 0

	# Reset speed to default (will be properly set by bullet's reset_for_pool method)
	# bullet.Speed = Config.BulletSpeed * 0.7  # This will be handled by the bullet itself

	# Reset particle effects
	if bullet.has_node("LightParticle"):
		bullet.get_node("LightParticle").visible = true
		bullet.get_node("LightParticle").emitting = true

	# Disconnect any existing signals to prevent issues
	if bullet.is_connected("area_entered", bullet, "_on_hit"):
		bullet.disconnect("area_entered", bullet, "_on_hit")

	# Reconnect the hit signal
	if bullet.has_method("_on_hit"):
		var _c = bullet.connect("area_entered", bullet, "_on_hit")

# Reset method for basic bullets (Bullet.gd, BulletSuper.gd)
func _reset_bullet_base(bullet):
	# Reset position and transform
	bullet.position = Vector2.ZERO
	bullet.rotation = 0
	bullet.scale = Vector2.ONE
	bullet.modulate = Color(1, 1, 1, 1)

	# Reset bullet state variables
	bullet.isDestroyed = false
	bullet.doCountTowardBulletsOnScreen = true
	bullet.isPassThrough = false
	bullet.canClash = false

	# Reset movement variables
	bullet.velocity = Vector2(0, -1)  # Default upward movement

	# Reset speed to default
	bullet.Speed = Config.BulletSpeed

	# Reset logging flag
	if bullet.has("wasLogged"):
		bullet.wasLogged = false

	# Reset particle effects if they exist
	if bullet.has_node("Particles2D"):
		bullet.get_node("Particles2D").visible = true
		bullet.get_node("Particles2D").emitting = true

	# Disconnect any existing signals to prevent issues
	if bullet.is_connected("area_entered", bullet, "_on_hit"):
		bullet.disconnect("area_entered", bullet, "_on_hit")

	# Reconnect the hit signal
	if bullet.has_method("_on_hit"):
		var _c = bullet.connect("area_entered", bullet, "_on_hit")

# Reset method for laser bullets (BulletLaser.gd)
func _reset_bullet_laser(bullet):
	# Reset position and transform
	bullet.position = Vector2.ZERO
	bullet.rotation = 0
	bullet.scale = Vector2.ONE
	bullet.modulate = Color(1, 1, 1, 1)

	# Reset bullet state variables
	bullet.isDestroyed = false
	bullet.doCountTowardBulletsOnScreen = true
	bullet.isPassThrough = false
	bullet.canClash = false

	# Reset laser-specific variables
	bullet.offsetY = 0

	# Reset speed to default
	bullet.Speed = Config.BulletSpeed

	# Reset laser collision
	if bullet.has_node("LaserCollision"):
		bullet.get_node("LaserCollision").disabled = false
		bullet.get_node("LaserCollision").scale = Vector2.ONE

	# Disconnect any existing signals to prevent issues
	if bullet.is_connected("area_entered", bullet, "_on_hit"):
		bullet.disconnect("area_entered", bullet, "_on_hit")

	# Reconnect the hit signal
	if bullet.has_method("_on_hit"):
		var _c = bullet.connect("area_entered", bullet, "_on_hit")

# Get a bullet from the pool and initialize it
func get_bullet(target = null):
	var bullet = get_object()
	if bullet:
		# Set pool reference so bullet can return itself (if supported)
		if bullet.has_method("set_bullet_pool"):
			bullet.set_bullet_pool(self)

		# Initialize the bullet after getting it from pool
		if bullet.has_method("start"):
			bullet.start(target)  # For homing bullets
		elif bullet.has_method("_ready"):
			# For basic bullets, call _ready to initialize
			bullet._ready()
	return bullet

# Return a bullet to the pool (called from bullet's destroy method)
func return_bullet(bullet):
	return_object(bullet)

# Get pool statistics
func get_stats():
	return {
		"available": available_objects.size(),
		"active": active_objects.size(),
		"total": available_objects.size() + active_objects.size()
	}

# Clean up the pool
func cleanup():
	# Free all available objects
	for obj in available_objects:
		if is_instance_valid(obj):
			obj.queue_free()
	available_objects.clear()

	# Note: Active objects should be returned to pool naturally
	# or will be cleaned up when their parent nodes are freed
	active_objects.clear()

func _exit_tree():
	cleanup()

# Smart pool cleanup - periodically remove excess inactive objects
func _perform_smart_cleanup():
	# If we have too many inactive objects compared to active ones
	var active_count = active_objects.size()
	var inactive_count = available_objects.size()

	# Only perform cleanup if we have more inactive objects than our max_inactive_size
	if inactive_count > max_inactive_size:
		# If active objects are a small fraction of inactive ones
		if active_count < (inactive_count * cleanup_threshold_ratio):
			# Calculate how many to remove (keep max_inactive_size objects)
			var to_remove = inactive_count - max_inactive_size

			# Free excess objects
			for _i in range(to_remove):
				if available_objects.size() > 0:
					var obj = available_objects.pop_front()  # Remove oldest objects first
					if is_instance_valid(obj):
						obj.queue_free()

			if PoolConfig.DEBUG_CLEANUP:
				print("Bullet pool cleanup: removed ", to_remove, " objects. New pool size: ",
					active_objects.size() + available_objects.size())

# Centralized bullet processing - call this from Game._process() instead of individual bullet processing
func process_all_bullets(delta):
	# Debug statistics output - every second
	debug_stats_counter += 1
	if debug_stats_counter >= debug_stats_frames:
		debug_stats_counter = 0
		if PoolConfig.COLLECT_STATS:
			var active_count = active_objects.size()
			var inactive_count = available_objects.size()
			var total_count = active_count + inactive_count
			# print("%s Pool Stats: Active=%d, Inactive=%d, Total=%d, MaxInactive=%d" % [
			# 	pool_type_name, active_count, inactive_count, total_count, max_inactive_size])

	# Smart cleanup check - periodically remove excess inactive objects
	cleanup_frame_counter += 1
	if cleanup_frame_counter >= cleanup_check_frames:
		cleanup_frame_counter = 0
		_perform_smart_cleanup()

	# Process all active bullets in this pool
	for i in range(active_objects.size() - 1, -1, -1):  # Iterate backwards for safe removal
		var bullet = active_objects[i]
		if bullet == null or not is_instance_valid(bullet) or bullet.isDestroyed:
			continue

		# Call the appropriate processing method based on bullet type
		if bullet.get_script() and bullet.get_script().get_path().ends_with("BulletHoming.gd"):
			_process_bullet_homing(bullet, delta)
		elif bullet.get_script() and bullet.get_script().get_path().ends_with("BulletLaser.gd"):
			_process_bullet_laser(bullet, delta)
		else:
			# Default processing for basic bullets (Bullet.gd, BulletSuper.gd)
			_process_bullet_base(bullet, delta)

# Process basic bullets (Bullet.gd, BulletSuper.gd)
func _process_bullet_base(bullet, delta):
	# Movement processing (from BulletBase._process) - FIXED: Consistent velocity calculation
	# The original code had a bug where Y was normalized*Speed but X was raw*Speed
	# This caused inconsistent speeds when velocity wasn't normalized
	var _velocity = bullet.velocity.normalized() * bullet.Speed
	# Don't override _velocity.x - keep consistent normalized scaling
	bullet.position += _velocity * delta * Vector2(1, bullet.getSpeedMod())

	# Destroy bullet if it goes off screen
	if bullet.position.y < -100:
		if not bullet.wasLogged:
			Global.GameScene.levelConductor.logBullet(true)
			bullet.wasLogged = true
		bullet.destroy(true)

# Process homing bullets (BulletHoming.gd)
func _process_bullet_homing(bullet, delta):
	# Cache current time once per frame
	bullet.current_time_cache = Tick.ms()

	# Remove bullet count after some time - use frame counter instead of Timer
	if bullet.bullet_count_timer < bullet.bullet_count_timeout:
		bullet.bullet_count_timer += 1
	elif bullet.bullet_count_timer == bullet.bullet_count_timeout:
		bullet.decreaseBulletCnt()
		bullet.bullet_count_timer += 1  # Prevent multiple calls

	bullet.checkDestroy()
	bullet.checkLightParticle()
	bullet.selectTarget()

	# Physics processing
	bullet.acceleration += bullet.seek()
	bullet.velocity += bullet.acceleration * delta
	bullet.velocity = bullet.velocity.limit_length(bullet.Speed)
	bullet.rotation = bullet.velocity.angle()
	bullet.position += bullet.velocity * delta

# Process laser bullets (BulletLaser.gd)
func _process_bullet_laser(bullet, delta):
	var speedScale = 1.1

	bullet.scale.y += delta * 500 * speedScale
	bullet.scale.x += delta * 2 * speedScale
	bullet.offsetY += delta * 1000 * speedScale

	bullet.global_position.x = Global.getPlayerPosition().x - 3 + randi() % 6
	bullet.global_position.y = Global.getPlayerPosition().y - bullet.offsetY

	# Destroy bullet if it reaches maximum scale
	if bullet.scale.y > 270 and not bullet.isDestroyed:
		bullet.destroy(true)
		Global.GameScene.levelConductor.logBullet(true)

# Create specialized pool instances for different bullet types (using PoolConfig)
static func create_bullet_pool(parent_node_arg):
	var bullet_pool = load("res://scripts/BulletPool.gd").new()
	var bullet_scene = preload("res://scenes/Bullet.tscn")
	bullet_pool.pool_type_name = "Basic"
	bullet_pool.max_inactive_size = PoolConfig.BULLET_POOL_MAX_INACTIVE
	bullet_pool.initialize(bullet_scene, parent_node_arg, PoolConfig.BULLET_POOL_INITIAL, PoolConfig.BULLET_POOL_MAX)
	return bullet_pool

static func create_bullet_super_pool(parent_node_arg):
	var bullet_pool = load("res://scripts/BulletPool.gd").new()
	var bullet_scene = preload("res://scenes/BulletSuper.tscn")
	bullet_pool.pool_type_name = "Super"
	bullet_pool.max_inactive_size = PoolConfig.BULLET_SUPER_POOL_MAX_INACTIVE
	bullet_pool.initialize(bullet_scene, parent_node_arg, PoolConfig.BULLET_SUPER_POOL_INITIAL, PoolConfig.BULLET_SUPER_POOL_MAX)
	return bullet_pool

static func create_bullet_homing_pool(parent_node_arg):
	var bullet_pool = load("res://scripts/BulletPool.gd").new()
	var bullet_scene = preload("res://scenes/BulletHoming.tscn")
	bullet_pool.pool_type_name = "Homing"
	bullet_pool.max_inactive_size = PoolConfig.BULLET_HOMING_POOL_MAX_INACTIVE
	bullet_pool.initialize(bullet_scene, parent_node_arg, PoolConfig.BULLET_HOMING_POOL_INITIAL, PoolConfig.BULLET_HOMING_POOL_MAX)
	return bullet_pool

static func create_bullet_laser_pool(parent_node_arg):
	var bullet_pool = load("res://scripts/BulletPool.gd").new()
	var bullet_scene = preload("res://scenes/BulletLaser.tscn")
	bullet_pool.pool_type_name = "Laser"
	bullet_pool.max_inactive_size = PoolConfig.BULLET_LASER_POOL_MAX_INACTIVE
	bullet_pool.initialize(bullet_scene, parent_node_arg, PoolConfig.BULLET_LASER_POOL_INITIAL, PoolConfig.BULLET_LASER_POOL_MAX)
	return bullet_pool

# Legacy method for backward compatibility
static func create_global_instance(parent_node_arg):
	return create_bullet_homing_pool(parent_node_arg)
