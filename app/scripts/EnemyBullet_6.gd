extends Node2D

export var steer_force = 10.0

export var canKillPlayer = true

func isDeadly():
	return canKillPlayer

var homingVelocity = Vector2.ZERO
var acceleration = Vector2.ZERO
var target = null

var steerTimer = 0
var destroyTimer = 0

var lifetimeMsec = 2000
var seekStartMsec = 100

var Speed = Config.EnemyBulletSpeed * 0.5

var isDestroyed = false

var doCountTowardBulletsOnScreen = true

var Sparkle = preload("res://scenes/Sparkle.tscn")

func canSteer():
	return Tick.ms() > steerTimer

func destroy(_force = false):

	if isDestroyed:
		return false
	
	isDestroyed = true

	if(doCountTowardBulletsOnScreen):
		Global.EnemyBulletsOnScreen -= 1
		Global.EnemyBulletsOnScreen = max(0, Global.EnemyBulletsOnScreen)

	var sparkle = Sparkle.instance()
	sparkle.position = position
	Global.GameScene.add_child(sparkle)

	queue_free()

func start(start_rotation):
	
	steer_force = steer_force+(randi()%15)

	steerTimer = Tick.ms() + seekStartMsec
	destroyTimer = Tick.ms() + lifetimeMsec

	global_transform = Transform2D()
	rotation_degrees = start_rotation+90
	homingVelocity = transform.x * Speed
	target = Global.getPlayer()

func seek():
	var steer = Vector2.ZERO

	if is_instance_valid(target) and canSteer():
		var desired = (target.global_position - position).normalized() * Speed
		steer = (desired - homingVelocity).normalized() * steer_force

	return steer

func checkDestroy():
	if isDestroyed:
		return

	if Tick.ms() > destroyTimer:
		steer_force = 0
	
	if Global.isOffScreen(global_position,10):
		self.destroy()
	
func showTail():
	$Tail.visible = true

func _process(_delta):
	checkDestroy()

func _physics_process(delta):

	if(Global.isOffScreenBottom(global_position, -200)):
		$Sprite.visible = false
		acceleration = Vector2.ZERO
	else:
		acceleration += seek()

	homingVelocity += acceleration * delta
	homingVelocity = homingVelocity.clamped(Speed)
	rotation = homingVelocity.angle()
	position += homingVelocity * delta
