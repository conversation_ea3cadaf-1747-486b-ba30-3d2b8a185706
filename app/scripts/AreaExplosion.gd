extends Area2D

func clearMe():
	Global.GameScene.decAreaExplosionCnt()
	call_deferred("queue_free")

func _ready():
	var _c = connect("area_entered",self,"_on_hit")
	$AnimationPlayer.play("AreaExplosion");
	Global.setTimeout(self,2,self,"clearMe")
	Global.GameScene.incAreaExplosionCnt()

func isAreaExplosion():
	return true;

func getDamagePoint():
	return 150

var canKillPlayer = true

func isDeadly():
	return canKillPlayer

var isMine = false

func _on_hit(target):
	# todo add boss hit !

	# is player / kill
	if target.has_method("isPlayer"):
		Global.callIfExists(target,"die")
		if(isMine):
			Achievments.acquire("mined")

	# # enemy hit and armed ( explode )
	# if(target.get("canKillPlayer")):
	# 	Global.callIfExists(target,"die")
