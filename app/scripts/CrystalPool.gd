# Crystal Pool for optimized crystal management
# Provides optimized crystal spawning with proper state reset

extends Node

class_name CrystalPool

# Pool configuration
var scene_resource
var parent_node
var available_objects = []
var active_objects = []
var max_pool_size = 100  # Set by initialize()
var initial_pool_size = 15  # Set by initialize()

# DEBUG: Infinite loop detection
var debug_loop_counter = 0
var debug_max_iterations = 50000  # Safety limit
var debug_last_active_size = 0
var debug_processing_start_time = 0.0
var debug_processing_timeout = 5.0  # 5 seconds timeout

# Smart cleanup configuration (uses PoolConfig values)
var max_inactive_size = PoolConfig.CRYSTAL_POOL_MAX_INACTIVE
var cleanup_check_frames = PoolConfig.CLEANUP_CHECK_FRAMES
var cleanup_frame_counter = 0
var cleanup_threshold_ratio = PoolConfig.CLEANUP_THRESHOLD_RATIO

# Debug statistics configuration
var debug_stats_frames = 60  # Output stats every second at 60fps
var debug_stats_counter = 0
var pool_type_name = "Crystal"  # For debug output identification

# Initialize the crystal pool
func initialize(scene, parent, initial_size = 15, max_size = 100):
	scene_resource = scene
	parent_node = parent
	initial_pool_size = initial_size
	max_pool_size = max_size

	# Pre-populate the pool
	for _i in range(initial_pool_size):
		var obj = scene_resource.instance()
		obj.set_process(false)
		obj.set_physics_process(false)
		obj.visible = false
		available_objects.append(obj)

# Get an object from the pool
func get_object():
	var obj

	if available_objects.size() > 0:
		# Reuse an existing object
		obj = available_objects.pop_back()
	else:
		# Create a new object if pool is empty and we haven't hit the limit
		if active_objects.size() < max_pool_size:
			obj = scene_resource.instance()
		else:
			# Pool is at capacity, return null or oldest active object
			# print("Warning: CrystalPool at capacity, creating new object anyway")
			obj = scene_resource.instance()

	# Activate the object (but disable individual processing - centralized now)
	obj.set_process(false)  # Individual processing disabled - handled by pool
	obj.set_physics_process(false)  # Individual physics processing disabled - handled by pool
	obj.visible = true

	# CRITICAL FIX: Defer the entire add_child operation to avoid physics query flushing error
	# This prevents the "area_set_shape_disabled" error when crystals are spawned during collision detection
	parent_node.call_deferred("add_child", obj)
	active_objects.append(obj)

	# CRITICAL FIX: Also defer enabling collision shapes to ensure it happens after add_child
	if obj.has_method("_enable_collision_shapes"):
		obj.call_deferred("_enable_collision_shapes")

	return obj

# Return an object to the pool
func return_object(obj):
	if obj == null or not is_instance_valid(obj):
		return

	# Remove from active tracking
	var index = active_objects.find(obj)
	if index >= 0:
		active_objects.remove(index)

	# Remove from scene tree
	if obj.get_parent():
		obj.get_parent().remove_child(obj)

	# Reset object state
	_reset_object(obj)

	# Deactivate the object
	obj.set_process(false)
	obj.set_physics_process(false)
	obj.visible = false

	# Return to available pool if we have space
	if available_objects.size() < max_pool_size:
		available_objects.append(obj)
	else:
		# Pool is full, destroy the object
		obj.queue_free()

# Reset method to properly reset crystal objects
func _reset_object(obj):
	if obj.has_method("reset_for_pool"):
		obj.reset_for_pool()
	else:
		# Fallback reset for crystal objects
		_reset_crystal_base(obj)

# Reset method for crystal objects (ChristalRigid.gd, CrystalSimple.gd)
func _reset_crystal_base(crystal):
	# Reset position and transform
	crystal.position = Vector2.ZERO
	crystal.rotation = 0
	crystal.scale = Vector2.ONE
	crystal.modulate = Color(1, 1, 1, 1)

	# Reset crystal state variables
	crystal.wasInit = false
	crystal.crystalType = Global.CrystalType.c5
	crystal.crystalValue = 5

	# Reset physics variables
	crystal.velocity = Vector2.ZERO
	crystal.gravity = 100.0  # Will be randomized in init
	crystal.initial_scatter_applied = false
	crystal.scatter_impulses_remaining = 1

	# Reset cached values
	crystal.cached_player_position = Vector2.ZERO
	crystal.cached_permanent_crystal_magnet = false
	crystal.cached_crystal_value_multiplier = 1.0
	crystal.player_position_update_counter = 0
	crystal.magnet_check_counter = 0
	crystal.cached_has_magnet_effect = false
	crystal.screen_check_counter = 0

	# Reset pool safety flag
	crystal.is_being_pooled = false

	# Reset magnet settings to defaults
	crystal.magnet_power = 50.0
	crystal.magnet_range = 400.0
	crystal.min_y_speed = 30.0
	crystal.y_slow_range = 350.0

	# Reset AnimatedSprite modulate
	if crystal.has_node("AnimatedSprite"):
		crystal.get_node("AnimatedSprite").modulate = Color(1.0, 1.0, 1.0, 1.0)

	# Important: Don't reconnect signals - they should remain connected from _ready()

# Get a crystal from the pool (without automatic initialization)
func get_crystal():
	var crystal = get_object()
	if crystal:
		# Set pool reference so crystal can return itself (if supported)
		if crystal.has_method("set_crystal_pool"):
			crystal.set_crystal_pool(self)
	return crystal

# Return a crystal to the pool (called from crystal's queue_free override)
func return_crystal(crystal):
	return_object(crystal)

# Get pool statistics
func get_stats():
	return {
		"available": available_objects.size(),
		"active": active_objects.size(),
		"total": available_objects.size() + active_objects.size()
	}

# Clean up the pool
func cleanup():
	# Free all available objects
	for obj in available_objects:
		if is_instance_valid(obj):
			obj.queue_free()
	available_objects.clear()

	# Note: Active objects should be returned to pool naturally
	# or will be cleaned up when their parent nodes are freed
	active_objects.clear()

func _exit_tree():
	cleanup()

# Smart pool cleanup - periodically remove excess inactive crystals
func _perform_smart_cleanup():
	# If we have too many inactive crystals compared to active ones
	var active_count = active_objects.size()
	var inactive_count = available_objects.size()

	# Only perform cleanup if we have more inactive crystals than our max_inactive_size
	if inactive_count > max_inactive_size:
		# If active crystals are a small fraction of inactive ones
		if active_count < (inactive_count * cleanup_threshold_ratio):
			# Calculate how many to remove (keep max_inactive_size crystals)
			var to_remove = inactive_count - max_inactive_size

			# Free excess crystals
			for _i in range(to_remove):
				if available_objects.size() > 0:
					var obj = available_objects.pop_front()  # Remove oldest crystals first
					if is_instance_valid(obj):
						obj.queue_free()

			if PoolConfig.DEBUG_CLEANUP:
				print("Crystal pool cleanup: removed ", to_remove, " crystals. New pool size: ",
					active_objects.size() + available_objects.size())

# Centralized crystal processing - call this from Game._process() instead of individual crystal processing
func process_all_crystals(delta):
	# DEBUG: Initialize loop detection
	debug_loop_counter = 0
	debug_processing_start_time = OS.get_ticks_msec() / 1000.0
	var initial_active_size = active_objects.size()

	# Debug statistics output - every second
	debug_stats_counter += 1
	if debug_stats_counter >= debug_stats_frames:
		debug_stats_counter = 0
		if PoolConfig.COLLECT_STATS:
			var active_count = active_objects.size()
			var inactive_count = available_objects.size()
			var total_count = active_count + inactive_count
			# print("%s Pool Stats: Active=%d, Inactive=%d, Total=%d, MaxInactive=%d" % [
			# 	pool_type_name, active_count, inactive_count, total_count, max_inactive_size])

	# Smart cleanup check - periodically remove excess inactive crystals
	cleanup_frame_counter += 1
	if cleanup_frame_counter >= cleanup_check_frames:
		cleanup_frame_counter = 0
		_perform_smart_cleanup()

	# DEBUG: Track processing time and iterations
	var current_time = OS.get_ticks_msec() / 1000.0
	if (current_time - debug_processing_start_time) > debug_processing_timeout:
		print("[CRITICAL] Crystal processing TIMEOUT after ", debug_processing_timeout, " seconds!")
		print("[CRITICAL] Loop counter: ", debug_loop_counter)
		print("[CRITICAL] Active crystals at start: ", initial_active_size)
		print("[CRITICAL] Active crystals now: ", active_objects.size())
		return  # Emergency exit

	# Process all active crystals in this pool
	# print("[DEBUG] Starting crystal loop - size: ", active_objects.size())
	for i in range(active_objects.size() - 1, -1, -1):  # Iterate backwards for safe removal
		# DEBUG: Increment loop counter and check for runaway loop
		debug_loop_counter += 1
		if debug_loop_counter > debug_max_iterations:
			print("[CRITICAL] INFINITE LOOP DETECTED! Iterations: ", debug_loop_counter)
			print("[CRITICAL] Current i: ", i)
			print("[CRITICAL] Active objects size: ", active_objects.size())
			print("[CRITICAL] Breaking out of loop!")
			break

		# DEBUG: Log every 1000 iterations
		# if debug_loop_counter % 1000 == 0:
			# print("[DEBUG] Loop iteration ", debug_loop_counter, " - i=", i, " size=", active_objects.size())

		var crystal = active_objects[i]
		if crystal == null or not is_instance_valid(crystal) or not crystal.wasInit:
			continue

		# SAFETY: Skip crystals that are being returned to pool to prevent recursive loops
		if crystal.is_being_pooled:
			continue

		# Call the crystal processing methods
		_process_crystal_base(crystal, delta)
		_physics_process_crystal_base(crystal, delta)

	# print("[DEBUG] Finished crystal processing - Final active count: ", active_objects.size(), " Total iterations: ", debug_loop_counter)

# Process crystal _process method logic (ChristalRigid._process)
func _process_crystal_base(crystal, _delta):
	# Update magnet effect cache less frequently (every 10th frame)
	crystal.magnet_check_counter += 1
	if crystal.magnet_check_counter >= 10:
		crystal.magnet_check_counter = 0
		crystal.cached_has_magnet_effect = Global.GameScene.hasPlayerEffect(Global.PlayerEffect.CRYSTAL_MAGNET)

	# Check if out of screen - reduce frequency (every 5th frame)
	crystal.screen_check_counter += 1
	if crystal.screen_check_counter >= 5:
		crystal.screen_check_counter = 0
		if Global.isOffScreenBottom(crystal.position, 200):
			# Return crystal to pool instead of queue_free
			if crystal.crystal_pool != null:
				# Set flag to prevent pool processing during return
				crystal.is_being_pooled = true
				# CRITICAL FIX: Use deferred call for off-screen crystals too
				crystal.call_deferred("_deferred_return_to_pool")
			else:
				crystal.queue_free()

# Process crystal _physics_process method logic (ChristalRigid._physics_process)
func _physics_process_crystal_base(crystal, delta):
	# Apply initial scatter impulses (replaces RigidBody2D apply_impulse)
	if not crystal.initial_scatter_applied and crystal.scatter_impulses_remaining > 0:
		# Apply random scatter velocity (equivalent to apply_impulse)
		var scatter_x = (randf() * 90 - 45) * 1.0  # Horizontal scatter
		var scatter_y = -(randf() * 50 + 20) * 1.0  # Upward scatter
		crystal.velocity += Vector2(scatter_x, scatter_y)
		crystal.scatter_impulses_remaining -= 1

		if crystal.scatter_impulses_remaining <= 0:
			crystal.initial_scatter_applied = true

	# Apply gravity
	crystal.velocity.y += crystal.gravity * delta

	# Update player position cache less frequently (every 5th frame)
	crystal.player_position_update_counter += 1
	if crystal.player_position_update_counter >= 5:
		crystal.player_position_update_counter = 0
		crystal.cached_player_position = Global.getPlayerPosition()

	# Magnet attraction logic (simplified and stronger)
	if crystal.doPushToPlayer():
		var distance = crystal.cached_player_position - crystal.global_position
		var distance_length = distance.length()

		if distance_length < crystal.magnet_range and distance_length > 5.0:
			# Strong magnet attraction - directly modify velocity toward player
			var magnet_strength = crystal.magnet_power * 3.0  # Use dedicated magnet power
			var attraction_force = distance.normalized() * magnet_strength

			# Apply stronger horizontal attraction
			crystal.velocity.x += attraction_force.x * delta

			# Apply Y-direction slowing based on distance to ship's Y coordinate
			var y_distance = abs(crystal.cached_player_position.y - crystal.global_position.y)
			if y_distance < crystal.y_slow_range:
				# Calculate slowing factor (closer = more slowing)
				var slow_factor = 1.0 - (y_distance / crystal.y_slow_range)
				slow_factor = pow(slow_factor, 2.0)  # Quadratic falloff for smoother effect

				# Only slow down if moving downward (positive Y velocity)
				if crystal.velocity.y > crystal.min_y_speed:
					var target_y_speed = max(crystal.min_y_speed, crystal.velocity.y * (1.0 - slow_factor * 0.8))
					crystal.velocity.y = lerp(crystal.velocity.y, target_y_speed, delta * 5.0)

			# Debug: Make crystal slightly brighter when magnet is active
			if crystal.has_node("AnimatedSprite"):
				crystal.get_node("AnimatedSprite").modulate = Color(1.2, 1.2, 1.2, 1.0)
	else:
		# Reset color when magnet is not active
		if crystal.has_node("AnimatedSprite"):
			crystal.get_node("AnimatedSprite").modulate = Color(1.0, 1.0, 1.0, 1.0)

	# Apply velocity to position
	crystal.global_position += crystal.velocity * delta

# Create specialized pool instances for different crystal types (using PoolConfig)
static func create_crystal_pool(parent_node_arg):
	var crystal_pool = load("res://scripts/CrystalPool.gd").new()
	var crystal_scene = preload("res://scenes/CrystalRigid.tscn")
	crystal_pool.max_inactive_size = PoolConfig.CRYSTAL_POOL_MAX_INACTIVE
	crystal_pool.initialize(crystal_scene, parent_node_arg, PoolConfig.CRYSTAL_POOL_INITIAL, PoolConfig.CRYSTAL_POOL_MAX)
	return crystal_pool

# Legacy method for backward compatibility
static func create_global_instance(parent_node_arg):
	return create_crystal_pool(parent_node_arg)
