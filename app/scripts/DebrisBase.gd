extends Area2D

signal debris_destroyed(object)

var health = 2000
var flashShader = preload("res://shaders/hit.tres")
var mode = Global.EnemyMode.IDLE
var HitColor = Color(1,1,1)
var canKillPlayer = true
var Explosion = preload("res://scenes/Explosion_1.tscn")

func isDeadly():
	return canKillPlayer && self.mode != Global.EnemyMode.DEAD

func getSprite():
	return $Sprite

var speedAddition = 0

func getSpeed():
	if(Global.GameScene.isPlayerReady()):
		speedAddition = 0
		return speedBase
	else:

		# gradually speed up to clean screen
		if(!Global.doThrottle('debris_speedup_throttle', 50)):
			speedAddition+=30

		return speedBase+speedAddition

var pointValue = Config.DebrisPoints
var speedBase = 0
var rotationBase = 0

func _process(delta):
	var yvelocity = getSpeed()
	position.y += (yvelocity*delta)
	rotation_degrees+=rotationBase*10*delta

export var isSmall = false

func getHealthLevelForIndicator():
	if(health<=500):
		return Global.OIType.Green
	elif(health>=1500):
		return Global.OIType.Red
	else:
		return Global.OIType.Yellow

func _ready():

	health = 100+randi()%1900
	health /= ShipSpecs.getSpecs().junk_strength_divider

	speedBase = Config.DebrisMinSpeed+randi()%(Config.DebrisMaxSpeed-Config.DebrisMinSpeed)
	rotationBase = -5+randf()*10

	position.y = -200
	position.x = 25+randi()%int(Global.getWindowSize().x-50)

	var _c = connect("area_entered",self,"_on_hit")
	self.getSprite().material = ShaderMaterial.new()
	self.getSprite().material.shader = flashShader

	randomize()

	getSprite().frame = randi()%(getSprite().frames.get_frame_count("default")-1)

	var _scale = (1.0 if isSmall else 0.7)+(randf()*1.3)
	scale = Vector2(_scale, _scale)

func _fon():
	self.getSprite().material.set_shader_param("active", 1)
	self.getSprite().material.set_shader_param("flash_color", self.HitColor)

func _foff():
	self.getSprite().material.set_shader_param("active", 0)

func flash():
	Global.playSound(SoundManager.HitSound, position, -10)

	_fon()
	Global.setTimeout(self,0.1,self,"_foff")

var lastBullet = null

func canBeDamaged():
	return true

func removeFromScreen():
	call_deferred("queue_free")

func die():

	if(self.mode == Global.EnemyMode.DEAD):
		return false

	self.mode = Global.EnemyMode.DEAD

	emit_signal("debris_destroyed",self)

	# play explosion
	var explosion = Explosion.instance()
	explosion.position = position;
	explosion.z_index  = z_index+1;
	Global.GameScene.add_child(explosion);
	Global.GameScene.shakeCamera(1)

	var tween = Global.createTween(self)
	tween.interpolate_property(self,"scale",Vector2(1,1),Vector2(0,0),0.2, Tween.TRANS_EXPO, Tween.EASE_OUT)
	tween.interpolate_property(self,"modulate:a",1,0,0.2, Tween.TRANS_QUAD, Tween.EASE_OUT)
	tween.connect("tween_all_completed",self,"removeFromScreen")
	tween.start()

	Global.GameScene.call_deferred("handleLoot",self.global_position)

	var _p = position
	Global.GameScene.spawnManyCrystals(10,_p,false,true,[Global.CrystalType.c5, Global.CrystalType.c10])

func checkHealth(doSpawnCrystal = false):

	if(self.health <= 0):
		# give points to the user
		Global.GameScene.addScore(pointValue, position)
		if(doSpawnCrystal && self.mode != Global.EnemyMode.DEAD):
			Global.GameScene.spawnCrystal(global_position,Global.CrystalType.c10);
		die()
	else:
		#taking damage
		flash()

func causeDamage(points, doSpawnCrystal = false):

	if(!self.canBeDamaged()):
		return false

	self.health-=points
	checkHealth(doSpawnCrystal)

func _on_hit(target):

	# no hit if enemy is off screen
	if Global.isOffScreenBottom(position, 50):
		return false

	# make sure that one bullet can only hit once
	if lastBullet == target:
		return false

	lastBullet = target

	if(target.has_method("getDamagePoint") && self.mode != Global.EnemyMode.DEAD):
		causeDamage(target.getDamagePoint(), target.has_method("isAreaExplosion"))
		Global.callIfExists(target,"destroy", true)
