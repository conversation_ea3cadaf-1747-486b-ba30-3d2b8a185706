extends "res://scripts/EnemyBase.gd"

func _ready():
	Bullet = preload("res://scenes/EnemyBullet_4.tscn")
	health = 100
	ExplosionColor = Color(0.2,0.2,1)
	species = "BlueEyes"
	pointValue = 4000

var _cnt = 0

func addBullet():
	var bullet = Bullet.instance()
	var _xskw = -0.2+(_cnt*0.1)
	_cnt+=1
	bullet.position = position;
	bullet.z_index  = z_index-1;
	bullet.velocity.x= _xskw
	bullet.randomJiggle = false;
	bullet.doJiggle = false;

	Global.GameScene.add_child(bullet);

func fireBullet(doForce = false):

	if(!doForce):
		if (Global.doThrottle(getId("",".Bullet"), Config.GlobalEnemyBulletThrottle) || !Global.isEnemyActive(mode)):
			return false

	# don't shoot if too close to bottom
	if Global.isOffScreenBottom(getCenterPosition(), -200):
		return false

	_cnt=0;
	for i in range(1, 6):
		Global.setTimeout(self, i*0.2, self, "addBullet")

	Global.EnemyBulletsOnScreen += 1
