extends "res://scripts/EnemyBulletBase.gd"

func canClash():
	return true

var startX = -999
var trigBase = 0
var trigType = "sin" # sin / cos

func moveBullet(delta):

	if(startX==-999):
		startX = position.x

	# move bullet
	var _velocity = velocity

	var trigPart = sin(trigBase)

	if(trigType=="cos"):
		trigPart = cos(trigBase+1)

	_velocity = _velocity * getBulletSpeed() * 0.4
	position += _velocity * delta
	position.x = startX+(trigPart*50)

	# destroy bullet if it goes off screen
	if position.y > Global.getWindowSize().y+100:
		destroy()

	trigBase+=delta*4
