extends "res://scripts/EnemyBase.gd"

func _ready():
	Bullet = preload("res://scenes/EnemyBullet_4.tscn")
	health = 60
	ExplosionColor = Color(0.2,0.2,1)
	species = "Alien Wings"
	pointValue = 6000
	setScratch(100.0)

var _cnt = 0
var _wing_animation_stopped = false

func _create_wingless_animation():
	# Create a new animation without wing tracks
	var animation = Animation.new()
	animation.set_name("wingless")
	animation.loop = true
	animation.length = 1.0
	
	# Add only the AnimatedSprite track (keep the body animation)
	var track_idx = animation.add_track(Animation.TYPE_VALUE)
	animation.track_set_path(track_idx, NodePath("AnimatedSprite:position"))
	animation.track_set_interpolation_type(track_idx, Animation.INTERPOLATION_LINEAR)
	
	# Set keyframes for the body animation
	animation.track_insert_key(track_idx, 0.0, Vector2(0.0844648, 7.99998))
	animation.track_insert_key(track_idx, 0.5, Vector2(0.084, 3))
	animation.track_insert_key(track_idx, 1.0, Vector2(0.0844648, 7.99998))
	
	return animation

func _check_wing_status():
	# Check if wings have been destroyed and stop animation if needed
	if not _wing_animation_stopped and has_node("AnimationPlayer"):
		var left_wing_missing = not has_node("Wing_left") or not is_instance_valid($Wing_left)
		var right_wing_missing = not has_node("Wing_right") or not is_instance_valid($Wing_right)
		
		if left_wing_missing or right_wing_missing:
			# CRITICAL FIX: Replace animation with wingless version to prevent node access errors
			$AnimationPlayer.stop()
			var wingless_anim = _create_wingless_animation()
			$AnimationPlayer.add_animation("wingless", wingless_anim)
			$AnimationPlayer.play("wingless")
			_wing_animation_stopped = true
			print("[DEBUG] Enemy_7 wings destroyed, switched to wingless animation")

func throttleDamage():
	return Global.doThrottle("Enemy7WingThrottle" + str(get_instance_id()), Config.BossDamageThrottleShort)

func getWingHealth():
	var h = 40
	if(has_node("Wing_left")):
		h = $Wing_left.health
	return h

func changeWingHealth(health):

	if(has_node("Wing_left")):
		$Wing_left.set("health",health)

	if(has_node("Wing_right")):
		$Wing_right.set("health",health)


func beforeDie():

	if((!has_node("Wing_left") and !has_node("Wing_right"))):
		Global.GameScene.spawnBonusCrystals(5,false,getCenterPosition(),true)

	# CRITICAL FIX: Stop wing animation to prevent node access errors
	if has_node("AnimationPlayer"):
		$AnimationPlayer.stop()

	for _i in range(0,10):
		var pos = getCenterPosition()+Vector2(randi()%50-50, randi()%20-20);
		Global.GameScene.spawnExplosion(pos,0.1*_i,Color(randf(),randf(),randf(),1))

	if(has_node("Wing_left")):
		for _i in range(0,10):
			var pos = getCenterPosition()+Vector2(-randi()%100, randi()%20-20);
			Global.GameScene.spawnExplosion(pos,0.1*_i,Color(randf(),randf(),randf(),1))

	if(has_node("Wing_right")):
		for _i in range(0,10):
			var pos = getCenterPosition()+Vector2(randi()%100, randi()%20-20);
			Global.GameScene.spawnExplosion(pos,0.1*_i,Color(randf(),randf(),randf(),1))

	return true

func setScratch(_percentage):
	if is_instance_valid($"Scratch"):
		$"Scratch".modulate.a  =(100-_percentage)/100.0

# overwrite damage function and if the enemy has wings cause 1/4 damage
func causeDamage(points, doSpawnCrystal = false):

	var divider = 1

	if(has_node("Wing_left")):
		divider *= 2

	if(has_node("Wing_right")):
		divider *= 2

	self.health-=points/divider

	var _percentage = Global.getPercentage(health,initialHealth);
	self.setScratch(_percentage)

	checkHealth(doSpawnCrystal)

func addBullet():
	var bullet = Bullet.instance()
	var _xskw = -0.2+(_cnt*0.1)
	_cnt+=1
	bullet.position = getCenterPosition();
	bullet.z_index  = z_index-1;
	bullet.velocity.x= _xskw
	bullet.randomJiggle = false;
	bullet.doJiggle = false;

	Global.GameScene.add_child_deferred(bullet);

func fireBullet(doForce = false):

	if(!doForce):
		if (Global.doThrottle(getId("",".Bullet"), Config.GlobalEnemyBulletThrottle) || !Global.isEnemyActive(mode)):
			return false

	# don't shoot if too close to bottom
	if Global.isOffScreenBottom(getCenterPosition(), -200):
		return false

	_cnt=0;
	for i in range(1, 6):
		Global.setTimeout(self, i*0.2, self, "addBullet")
	
	# fire bullet from wings - with additional safety checks
	if(has_node("Wing_left") and is_instance_valid($Wing_left)):
		$Wing_left.call_deferred("fireBullet",true);

	if(has_node("Wing_right") and is_instance_valid($Wing_right)):
		$Wing_right.call_deferred("fireBullet",true);

	Global.EnemyBulletsOnScreen += 1

func _on_wing_destroyed(wing_name):
	# CRITICAL FIX: Handle immediate wing destruction notification
	print("[DEBUG] Enemy_7 received wing destruction notification for: ", wing_name)
	if not _wing_animation_stopped and has_node("AnimationPlayer"):
		$AnimationPlayer.stop()
		var wingless_anim = _create_wingless_animation()
		$AnimationPlayer.add_animation("wingless", wingless_anim)
		$AnimationPlayer.play("wingless")
		_wing_animation_stopped = true
		print("[DEBUG] Enemy_7 switched to wingless animation due to wing destruction")

func _process(delta):
	# Check wing status periodically to stop animation if wings are destroyed (backup)
	_check_wing_status()
	# Call parent process
	._process(delta)
