[gd_scene load_steps=3 format=2]

[ext_resource path="res://scripts/PlayerAvoidBonus.gd" type="Script" id=1]

[sub_resource type="CapsuleShape2D" id=1]
radius = 33.0
height = 0.0

[node name="PlayerAvoidDetector" type="Area2D"]
collision_mask = 44
script = ExtResource( 1 )
__meta__ = {
"_edit_horizontal_guides_": [ -42.0, 42.0 ],
"_edit_vertical_guides_": [ -39.0, 39.0 ]
}

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
modulate = Color( 0.631373, 0.0431373, 0.568627, 1 )
position = Vector2( 0, -4 )
shape = SubResource( 1 )
