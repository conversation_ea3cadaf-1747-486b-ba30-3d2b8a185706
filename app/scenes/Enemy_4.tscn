[gd_scene load_steps=6 format=2]

[ext_resource path="res://assets/Enemy - Spicy roid - Frame 1.png" type="Texture" id=1]
[ext_resource path="res://assets/Enemy - Spicy roid - Frame 2.png" type="Texture" id=2]
[ext_resource path="res://scripts/Enemy_4.gd" type="Script" id=3]

[sub_resource type="SpriteFrames" id=1]
animations = [ {
"frames": [ ExtResource( 1 ), ExtResource( 2 ) ],
"loop": true,
"name": "default",
"speed": 1.0
} ]

[sub_resource type="RectangleShape2D" id=2]
extents = Vector2( 11, 12.3333 )

[node name="Enemy_4" type="Area2D"]
collision_layer = 4
collision_mask = 3
script = ExtResource( 3 )

[node name="AnimatedSprite" type="AnimatedSprite" parent="."]
scale = Vector2( 2.75, 2.75 )
frames = SubResource( 1 )
playing = true

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
visible = false
position = Vector2( 0.5, -0.5 )
scale = Vector2( 1.5, 1.5 )
shape = SubResource( 2 )
