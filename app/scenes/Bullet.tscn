[gd_scene load_steps=7 format=2]

[ext_resource path="res://assets/bullet1.png" type="Texture" id=1]
[ext_resource path="res://scripts/Bullet.gd" type="Script" id=2]

[sub_resource type="Gradient" id=2]
offsets = PoolRealArray( 0, 0.334821, 0.78125, 1 )
colors = PoolColorArray( 0, 1, 0.806641, 1, 0.74707, 0.169258, 0.169258, 1, 1, 0.626953, 0, 1, 1, 0.886275, 0, 0 )

[sub_resource type="GradientTexture" id=3]
gradient = SubResource( 2 )

[sub_resource type="ParticlesMaterial" id=4]
lifetime_randomness = 0.5
emission_shape = 2
emission_box_extents = Vector3( 1, 1, 1 )
flag_disable_z = true
gravity = Vector3( 0, 98, 0 )
orbit_velocity = 0.0
orbit_velocity_random = 0.0
scale = 0.5
scale_random = 1.0
color_ramp = SubResource( 3 )

[sub_resource type="RectangleShape2D" id=1]
extents = Vector2( 1, 2 )

[node name="Bullet" type="Area2D"]
collision_layer = 2
collision_mask = 44
script = ExtResource( 2 )

[node name="Sprite" type="Sprite" parent="."]
scale = Vector2( 2, 2 )
texture = ExtResource( 1 )

[node name="Particles2D" type="Particles2D" parent="Sprite"]
modulate = Color( 1, 1, 1, 0.235294 )
position = Vector2( 0, 3 )
amount = 15
lifetime = 0.5
process_material = SubResource( 4 )

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
scale = Vector2( 2, 2 )
shape = SubResource( 1 )
