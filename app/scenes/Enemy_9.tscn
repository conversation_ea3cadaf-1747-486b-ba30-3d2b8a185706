[gd_scene load_steps=6 format=2]

[ext_resource path="res://assets/Enemy - Ghost - Frame 2.png" type="Texture" id=1]
[ext_resource path="res://scripts/Enemy_9.gd" type="Script" id=2]
[ext_resource path="res://assets/Enemy - Ghost - Frame 1.png" type="Texture" id=3]

[sub_resource type="SpriteFrames" id=3]
animations = [ {
"frames": [ ExtResource( 3 ), ExtResource( 1 ) ],
"loop": true,
"name": "default",
"speed": 2.0
} ]

[sub_resource type="RectangleShape2D" id=4]
extents = Vector2( 25, 22 )

[node name="Enemy_9" type="Area2D"]
collision_layer = 4
collision_mask = 3
script = ExtResource( 2 )

[node name="AnimatedSprite" type="AnimatedSprite" parent="."]
scale = Vector2( 1.75, 1.75 )
frames = SubResource( 3 )
playing = true

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
position = Vector2( 1, -1 )
shape = SubResource( 4 )
