[gd_scene load_steps=16 format=2]

[ext_resource path="res://assets/hero_wing_green_left.png" type="Texture" id=1]
[ext_resource path="res://assets/hero_wing_red_left.png" type="Texture" id=2]
[ext_resource path="res://assets/hero_wing_red_right.png" type="Texture" id=3]
[ext_resource path="res://assets/hero_wing_green_right.png" type="Texture" id=4]
[ext_resource path="res://assets/hero_wing_blue_left.png" type="Texture" id=5]
[ext_resource path="res://assets/hero_wing_blue_right.png" type="Texture" id=6]
[ext_resource path="res://scripts/player_wing.gd" type="Script" id=7]
[ext_resource path="res://addons/kenney_particle_pack/16/slash_02_16.png" type="Texture" id=8]

[sub_resource type="Gradient" id=4]
colors = PoolColorArray( 1, 1, 1, 1, 0, 1, 0.905882, 0.109804 )

[sub_resource type="GradientTexture" id=5]
gradient = SubResource( 4 )

[sub_resource type="Curve" id=6]
_data = [ Vector2( 0, 0 ), 0.0, 0.0, 0, 0, Vector2( 0.853909, 1 ), 0.0, 0.0, 0, 0, Vector2( 1, 1 ), 0.0, 0.0, 0, 0 ]

[sub_resource type="CurveTexture" id=7]
curve = SubResource( 6 )

[sub_resource type="ParticlesMaterial" id=3]
emission_shape = 1
emission_sphere_radius = 1.0
flag_disable_z = true
gravity = Vector3( 0, 98, 0 )
orbit_velocity = 0.0
orbit_velocity_random = 0.0
scale_curve = SubResource( 7 )
color_ramp = SubResource( 5 )

[sub_resource type="SpriteFrames" id=1]
animations = [ {
"frames": [ ExtResource( 5 ), ExtResource( 6 ) ],
"loop": true,
"name": "blue",
"speed": 5.0
}, {
"frames": [ ExtResource( 1 ), ExtResource( 4 ) ],
"loop": true,
"name": "green",
"speed": 5.0
}, {
"frames": [ ExtResource( 2 ), ExtResource( 3 ) ],
"loop": true,
"name": "red",
"speed": 5.0
} ]

[sub_resource type="CapsuleShape2D" id=2]
radius = 8.0
height = 14.0

[node name="Wing" type="Area2D"]
collision_mask = 60
script = ExtResource( 7 )

[node name="Particles2D" type="Particles2D" parent="."]
position = Vector2( 0, 13 )
lifetime = 0.5
speed_scale = 2.0
randomness = 0.5
process_material = SubResource( 3 )
texture = ExtResource( 8 )

[node name="WingSprite" type="AnimatedSprite" parent="."]
scale = Vector2( 1.75, 1.75 )
frames = SubResource( 1 )
animation = "blue"

[node name="CollisionShape" type="CollisionShape2D" parent="."]
shape = SubResource( 2 )
