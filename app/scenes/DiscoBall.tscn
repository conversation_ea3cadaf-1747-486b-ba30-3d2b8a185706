[gd_scene load_steps=9 format=2]

[ext_resource path="res://assets/disco_ball_4.png" type="Texture" id=1]
[ext_resource path="res://assets/disco_ball_1.png" type="Texture" id=2]
[ext_resource path="res://assets/disco_ball_2.png" type="Texture" id=3]
[ext_resource path="res://assets/disco_ball_3.png" type="Texture" id=4]
[ext_resource path="res://scripts/DiscoBall.gd" type="Script" id=5]

[sub_resource type="SpriteFrames" id=1]
animations = [ {
"frames": [ ExtResource( 2 ), ExtResource( 3 ), ExtResource( 4 ), ExtResource( 1 ) ],
"loop": true,
"name": "default",
"speed": 5.0
} ]

[sub_resource type="CircleShape2D" id=2]
radius = 32.0

[sub_resource type="ParticlesMaterial" id=3]
emission_shape = 1
emission_sphere_radius = 28.07
flag_disable_z = true
direction = Vector3( 0, 0, 0 )
spread = 0.0
gravity = Vector3( 0, 0, 0 )
orbit_velocity = 0.0
orbit_velocity_random = 0.0
scale = 7.0
scale_random = 1.0

[node name="DiscoBall" type="Area2D"]
collision_layer = 4
collision_mask = 2
script = ExtResource( 5 )

[node name="AnimatedSprite" type="AnimatedSprite" parent="."]
frames = SubResource( 1 )
playing = true

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource( 2 )

[node name="Particles2D" type="Particles2D" parent="."]
modulate = Color( 1, 1, 1, 0.47451 )
amount = 5
lifetime = 0.2
process_material = SubResource( 3 )
