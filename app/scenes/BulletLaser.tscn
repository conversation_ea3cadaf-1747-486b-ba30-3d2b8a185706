[gd_scene load_steps=4 format=2]

[ext_resource path="res://assets/laser.png" type="Texture" id=1]
[ext_resource path="res://scripts/BulletLaser.gd" type="Script" id=2]

[sub_resource type="RectangleShape2D" id=1]
extents = Vector2( 1, 1 )

[node name="BulletLaser" type="Area2D"]
collision_layer = 2
collision_mask = 44
script = ExtResource( 2 )

[node name="LaserCollision" type="CollisionShape2D" parent="."]
position = Vector2( 0, 1 )
shape = SubResource( 1 )

[node name="LaserSprite" type="Sprite" parent="."]
position = Vector2( 0, 1 )
scale = Vector2( 1, 0.0625 )
texture = ExtResource( 1 )
