[gd_scene load_steps=30 format=2]

[ext_resource path="res://assets/SpiderBoss/big-leg-armored.png" type="Texture" id=1]
[ext_resource path="res://assets/SpiderBoss/big-leg.png" type="Texture" id=2]
[ext_resource path="res://assets/SpiderBoss/claw.png" type="Texture" id=3]
[ext_resource path="res://assets/SpiderBoss/rod.png" type="Texture" id=4]
[ext_resource path="res://assets/SpiderBoss/big-connector.png" type="Texture" id=5]
[ext_resource path="res://assets/SpiderBoss/small-connector.png" type="Texture" id=6]
[ext_resource path="res://scenes/Bosses/Boss4/SpiderLegLarge.gd" type="Script" id=7]
[ext_resource path="res://addons/kenney_particle_pack/spark_02.png" type="Texture" id=8]
[ext_resource path="res://addons/kenney_particle_pack/spark_04.png" type="Texture" id=9]

[sub_resource type="Shader" id=10]
resource_local_to_scene = true
code = "shader_type canvas_item;

uniform bool active = false;
uniform bool gray = false;
uniform vec4 flash_color: hint_color = vec4(1.0, 1.0, 1.0, 1.0);

void fragment() {

	vec4 previous_color = texture(TEXTURE, UV);

	vec4 white_color = flash_color;
	white_color.a = previous_color.a;
	float avg = (previous_color.r + previous_color.g + previous_color.b)/3.0;
	vec4 black_color = vec4(avg/2.0, avg/2.0, avg/2.0, previous_color.a);

	vec4 new_color = previous_color;

	if (active == true)
	{
		new_color = white_color;

		if (gray == true)
		{
			new_color = black_color;
		}
	}


	COLOR = new_color;
}"

[sub_resource type="ShaderMaterial" id=11]
resource_local_to_scene = true
shader = SubResource( 10 )
shader_param/active = false
shader_param/gray = false
shader_param/flash_color = Color( 1, 1, 1, 1 )

[sub_resource type="Shader" id=12]
resource_local_to_scene = true
code = "shader_type canvas_item;

uniform bool active = false;
uniform bool gray = false;
uniform vec4 flash_color: hint_color = vec4(1.0, 1.0, 1.0, 1.0);

void fragment() {

	vec4 previous_color = texture(TEXTURE, UV);

	vec4 white_color = flash_color;
	white_color.a = previous_color.a;
	float avg = (previous_color.r + previous_color.g + previous_color.b)/3.0;
	vec4 black_color = vec4(avg/2.0, avg/2.0, avg/2.0, previous_color.a);

	vec4 new_color = previous_color;

	if (active == true)
	{
		new_color = white_color;

		if (gray == true)
		{
			new_color = black_color;
		}
	}


	COLOR = new_color;
}"

[sub_resource type="ShaderMaterial" id=13]
resource_local_to_scene = true
shader = SubResource( 12 )
shader_param/active = false
shader_param/gray = false
shader_param/flash_color = Color( 1, 1, 1, 1 )

[sub_resource type="Shader" id=8]
resource_local_to_scene = true
code = "shader_type canvas_item;

uniform bool active = false;
uniform bool gray = false;
uniform vec4 flash_color: hint_color = vec4(1.0, 1.0, 1.0, 1.0);

void fragment() {

	vec4 previous_color = texture(TEXTURE, UV);

	vec4 white_color = flash_color;
	white_color.a = previous_color.a;
	float avg = (previous_color.r + previous_color.g + previous_color.b)/3.0;
	vec4 black_color = vec4(avg/2.0, avg/2.0, avg/2.0, previous_color.a);

	vec4 new_color = previous_color;

	if (active == true)
	{
		new_color = white_color;

		if (gray == true)
		{
			new_color = black_color;
		}
	}


	COLOR = new_color;
}"

[sub_resource type="ShaderMaterial" id=14]
resource_local_to_scene = true
shader = SubResource( 8 )
shader_param/active = false
shader_param/gray = false
shader_param/flash_color = Color( 1, 1, 1, 1 )

[sub_resource type="Shader" id=15]
resource_local_to_scene = true
code = "shader_type canvas_item;

uniform bool active = false;
uniform bool gray = false;
uniform vec4 flash_color: hint_color = vec4(1.0, 1.0, 1.0, 1.0);

void fragment() {

	vec4 previous_color = texture(TEXTURE, UV);

	vec4 white_color = flash_color;
	white_color.a = previous_color.a;
	float avg = (previous_color.r + previous_color.g + previous_color.b)/3.0;
	vec4 black_color = vec4(avg/2.0, avg/2.0, avg/2.0, previous_color.a);

	vec4 new_color = previous_color;

	if (active == true)
	{
		new_color = white_color;

		if (gray == true)
		{
			new_color = black_color;
		}
	}


	COLOR = new_color;
}"

[sub_resource type="ShaderMaterial" id=16]
resource_local_to_scene = true
shader = SubResource( 15 )
shader_param/active = false
shader_param/gray = false
shader_param/flash_color = Color( 1, 1, 1, 1 )

[sub_resource type="CircleShape2D" id=7]
radius = 7.0

[sub_resource type="CapsuleShape2D" id=6]
radius = 2.37645
height = 31.2317

[sub_resource type="CapsuleShape2D" id=4]
radius = 6.72426
height = 31.2317

[sub_resource type="Gradient" id=58]
offsets = PoolRealArray( 0.002849, 0.381766, 0.666667 )
colors = PoolColorArray( 0.440329, 0.827778, 0.997559, 1, 0, 0.976562, 1, 1, 0, 1, 0.227451, 0 )

[sub_resource type="GradientTexture" id=57]
gradient = SubResource( 58 )

[sub_resource type="ParticlesMaterial" id=59]
emission_shape = 1
emission_sphere_radius = 10.65
flag_align_y = true
flag_rotate_y = true
flag_disable_z = true
direction = Vector3( 0, 0, 0 )
spread = 0.0
gravity = Vector3( 0, 0, 0 )
orbit_velocity = 0.0
orbit_velocity_random = 0.0
radial_accel = 100.0
radial_accel_random = 1.0
scale = 0.1
scale_random = 0.1
color_ramp = SubResource( 57 )

[sub_resource type="CapsuleShape2D" id=5]
radius = 6.31475
height = 16.9786

[sub_resource type="Gradient" id=61]
offsets = PoolRealArray( 0.002849, 0.381766, 0.666667 )
colors = PoolColorArray( 0.440329, 0.827778, 0.997559, 1, 0, 0.976562, 1, 1, 0, 1, 0.227451, 0 )

[sub_resource type="GradientTexture" id=62]
gradient = SubResource( 61 )

[sub_resource type="ParticlesMaterial" id=60]
emission_shape = 1
emission_sphere_radius = 10.65
flag_align_y = true
flag_rotate_y = true
flag_disable_z = true
direction = Vector3( 0, 0, 0 )
spread = 0.0
gravity = Vector3( 0, 0, 0 )
orbit_velocity = 0.0
orbit_velocity_random = 0.0
radial_accel = 100.0
radial_accel_random = 1.0
scale = 0.1
scale_random = 0.1
color_ramp = SubResource( 62 )

[sub_resource type="Animation" id=3]
length = 0.001
tracks/0/type = "value"
tracks/0/path = NodePath("Base:rotation_degrees")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ -9020.53 ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("Base/Big-leg/Big-connector/Big-leg-armored:rotation_degrees")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ -367.345 ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("Base/Big-leg/Big-connector:rotation_degrees")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ 696.789 ]
}
tracks/3/type = "value"
tracks/3/path = NodePath("Base/Big-leg:rotation_degrees")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ -899.999 ]
}
tracks/4/type = "value"
tracks/4/path = NodePath("Base/Big-leg/Big-connector/Big-leg-armored/Small-connector/Rod:rotation_degrees")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ 230.711 ]
}
tracks/5/type = "value"
tracks/5/path = NodePath("Base/Big-leg/Big-connector/Big-leg-armored/Small-connector:rotation_degrees")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ -901.546 ]
}
tracks/6/type = "value"
tracks/6/path = NodePath("Base/Big-leg/Big-connector/Big-leg-armored/Small-connector/Rod/Claw:rotation_degrees")
tracks/6/interp = 1
tracks/6/loop_wrap = true
tracks/6/imported = false
tracks/6/enabled = true
tracks/6/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ -211.929 ]
}

[sub_resource type="Animation" id=2]
resource_name = "iddle"
length = 3.0
loop = true
tracks/0/type = "value"
tracks/0/path = NodePath("Base/Big-leg/Big-connector/Big-leg-armored/Small-connector:rotation_degrees")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 1.4, 3 ),
"transitions": PoolRealArray( 1, 0.5, -0.5 ),
"update": 0,
"values": [ -901.546, -901.546, -901.546 ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("Base/Big-leg/Big-connector/Big-leg-armored:rotation_degrees")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 1, 3 ),
"transitions": PoolRealArray( 1, 0.5, 2 ),
"update": 0,
"values": [ -359.999, -370.506, -359.999 ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("Base/Big-leg/Big-connector:rotation_degrees")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 0.8, 3 ),
"transitions": PoolRealArray( 1, 0.5, 2 ),
"update": 0,
"values": [ 696.789, 696.789, 696.789 ]
}
tracks/3/type = "value"
tracks/3/path = NodePath("Base/Big-leg:rotation_degrees")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0, 1, 3 ),
"transitions": PoolRealArray( 1, 0.5, 1 ),
"update": 0,
"values": [ -899.999, -881.73, -899.999 ]
}
tracks/4/type = "value"
tracks/4/path = NodePath("Base:rotation_degrees")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/keys = {
"times": PoolRealArray( 0, 1.3, 3 ),
"transitions": PoolRealArray( 1, 0.5, 1 ),
"update": 0,
"values": [ -9020.53, -9020.53, -9020.53 ]
}
tracks/5/type = "value"
tracks/5/path = NodePath("Base/Big-leg/Big-connector/Big-leg-armored/Small-connector/Rod:rotation_degrees")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/keys = {
"times": PoolRealArray( 0, 1.7, 3 ),
"transitions": PoolRealArray( 1, 0.5, 1 ),
"update": 0,
"values": [ 240.0, 205.951, 240.0 ]
}
tracks/6/type = "value"
tracks/6/path = NodePath("Base/Big-leg/Big-connector/Big-leg-armored/Small-connector/Rod/Claw:rotation_degrees")
tracks/6/interp = 1
tracks/6/loop_wrap = true
tracks/6/imported = false
tracks/6/enabled = true
tracks/6/keys = {
"times": PoolRealArray( 0, 2.3, 3 ),
"transitions": PoolRealArray( 1, 0.5, 1 ),
"update": 0,
"values": [ -195.0, -228.073, -195.0 ]
}

[node name="SpiderLeg" type="Node2D"]
rotation = 571.77
script = ExtResource( 7 )

[node name="Base" type="Sprite" parent="."]
rotation = -157.438
texture = ExtResource( 5 )
__meta__ = {
"_edit_bone_": true
}

[node name="Big-leg" type="Sprite" parent="Base"]
material = SubResource( 11 )
position = Vector2( -7.62939e-06, 1.90735e-06 )
rotation = -15.7079
texture = ExtResource( 2 )
offset = Vector2( 10.1054, 0 )
__meta__ = {
"_edit_bone_": true
}

[node name="Big-connector" type="Sprite" parent="Base/Big-leg"]
position = Vector2( 21, 0.00956917 )
rotation = 12.1613
texture = ExtResource( 5 )
__meta__ = {
"_edit_bone_": true
}

[node name="Big-leg-armored" type="Sprite" parent="Base/Big-leg/Big-connector"]
material = SubResource( 13 )
position = Vector2( 1, 0.000454901 )
rotation = -6.41138
texture = ExtResource( 1 )
offset = Vector2( 19.371, -9.53674e-07 )
__meta__ = {
"_edit_bone_": true
}

[node name="Small-connector" type="Sprite" parent="Base/Big-leg/Big-connector/Big-leg-armored"]
position = Vector2( 38, 0.0173174 )
rotation = -15.7349
texture = ExtResource( 6 )
__meta__ = {
"_edit_bone_": true
}

[node name="Rod" type="Sprite" parent="Base/Big-leg/Big-connector/Big-leg-armored/Small-connector"]
material = SubResource( 14 )
position = Vector2( 0.133183, 0.00686385 )
rotation = 4.02667
texture = ExtResource( 4 )
offset = Vector2( 1.2964e-06, -14.8668 )
__meta__ = {
"_edit_bone_": true
}

[node name="Claw" type="Sprite" parent="Base/Big-leg/Big-connector/Big-leg-armored/Small-connector/Rod"]
material = SubResource( 16 )
position = Vector2( 0.00687055, -29.8668 )
rotation = -3.69886
texture = ExtResource( 3 )
offset = Vector2( 1.35999, 3.54079 )
__meta__ = {
"_edit_bone_": true
}

[node name="Area2D" type="Area2D" parent="Base/Big-leg/Big-connector/Big-leg-armored/Small-connector/Rod/Claw"]
collision_layer = 4
collision_mask = 34

[node name="CollisionShape2D" type="CollisionShape2D" parent="Base/Big-leg/Big-connector/Big-leg-armored/Small-connector/Rod/Claw/Area2D"]
position = Vector2( 0.346222, 3.90971 )
rotation = -1.57079
shape = SubResource( 7 )

[node name="Area2D" type="Area2D" parent="Base/Big-leg/Big-connector/Big-leg-armored/Small-connector/Rod"]
collision_layer = 4
collision_mask = 34

[node name="CollisionShape2D" type="CollisionShape2D" parent="Base/Big-leg/Big-connector/Big-leg-armored/Small-connector/Rod/Area2D"]
position = Vector2( 0.334305, -15.3823 )
rotation = 3.13866
shape = SubResource( 6 )

[node name="Area2D" type="Area2D" parent="Base/Big-leg/Big-connector/Big-leg-armored"]
collision_layer = 4
collision_mask = 34

[node name="CollisionShape2D" type="CollisionShape2D" parent="Base/Big-leg/Big-connector/Big-leg-armored/Area2D"]
position = Vector2( 20.2025, 0.0105782 )
rotation = -1.57079
shape = SubResource( 4 )

[node name="Crack" type="Sprite" parent="Base/Big-leg/Big-connector/Big-leg-armored"]
modulate = Color( 0, 0, 0, 0 )
position = Vector2( 21.0837, -0.183044 )
scale = Vector2( 0.112048, -0.0293929 )
texture = ExtResource( 8 )

[node name="Particles2D" type="Particles2D" parent="Base/Big-leg/Big-connector/Big-leg-armored/Crack"]
modulate = Color( 1, 1, 1, 0.745098 )
position = Vector2( -48.9271, -64.408 )
amount = 15
lifetime = 0.5
local_coords = false
process_material = SubResource( 59 )
texture = ExtResource( 9 )

[node name="Area2D" type="Area2D" parent="Base/Big-leg"]
collision_layer = 4
collision_mask = 34

[node name="CollisionShape2D" type="CollisionShape2D" parent="Base/Big-leg/Area2D"]
position = Vector2( 9.48055, 0.344908 )
rotation = -1.57079
shape = SubResource( 5 )

[node name="Crack" type="Sprite" parent="Base/Big-leg"]
modulate = Color( 0, 0, 0, 0 )
position = Vector2( 9.38567, -0.155365 )
scale = Vector2( 0.0689052, 0.0212176 )
texture = ExtResource( 8 )

[node name="Particles2D" type="Particles2D" parent="Base/Big-leg/Crack"]
modulate = Color( 1, 1, 1, 0.745098 )
position = Vector2( -48.9271, -64.408 )
amount = 15
lifetime = 0.5
local_coords = false
process_material = SubResource( 60 )
texture = ExtResource( 9 )

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
autoplay = "iddle"
anims/RESET = SubResource( 3 )
anims/iddle = SubResource( 2 )
