[gd_scene load_steps=25 format=2]

[ext_resource path="res://assets/huge_cristal_base.png" type="Texture" id=1]
[ext_resource path="res://scenes/Bosses/Boss4/SpiderLegLarge.tscn" type="PackedScene" id=2]
[ext_resource path="res://assets/SpiderBoss/body.png" type="Texture" id=3]
[ext_resource path="res://scenes/Bosses/Boss4/ShieldEnergyBall.tscn" type="PackedScene" id=4]
[ext_resource path="res://scenes/Bosses/Boss4/SpiderEnergyShieldBeam.tscn" type="PackedScene" id=5]
[ext_resource path="res://addons/kenney_particle_pack/slash_01.png" type="Texture" id=6]
[ext_resource path="res://scenes/Bosses/Boss4/SpiderEnergyShieldBeamLarge.tscn" type="PackedScene" id=7]
[ext_resource path="res://scenes/Bosses/Boss4/SpiderBoss.gd" type="Script" id=8]
[ext_resource path="res://scenes/Bosses/Boss4/Beam.tscn" type="PackedScene" id=9]
[ext_resource path="res://addons/kenney_particle_pack/16/star_06_16.png" type="Texture" id=10]
[ext_resource path="res://addons/kenney_particle_pack/spark_02.png" type="Texture" id=11]
[ext_resource path="res://addons/kenney_particle_pack/spark_04.png" type="Texture" id=12]

[sub_resource type="Gradient" id=13]
offsets = PoolRealArray( 0, 0.645788 )
colors = PoolColorArray( 0.989258, 1, 0, 1, 1, 1, 1, 0 )

[sub_resource type="GradientTexture" id=14]
gradient = SubResource( 13 )

[sub_resource type="ParticlesMaterial" id=15]
emission_shape = 2
emission_box_extents = Vector3( 80, 30, 1 )
flag_disable_z = true
direction = Vector3( 0, 0, 0 )
gravity = Vector3( 0, 0, 0 )
orbit_velocity = 0.0
orbit_velocity_random = 0.0
tangential_accel = 100.0
tangential_accel_random = 1.0
scale = 20.0
scale_random = 1.0
color = Color( 0.992157, 1, 0, 1 )
color_ramp = SubResource( 14 )
hue_variation = 0.07
hue_variation_random = 1.0

[sub_resource type="Shader" id=11]
resource_local_to_scene = true
code = "shader_type canvas_item;

uniform bool active = false;
uniform bool gray = false;
uniform vec4 flash_color: hint_color = vec4(1.0, 1.0, 1.0, 1.0);

void fragment() {

	vec4 previous_color = texture(TEXTURE, UV);

	vec4 white_color = flash_color;
	white_color.a = previous_color.a;
	float avg = (previous_color.r + previous_color.g + previous_color.b)/3.0;
	vec4 black_color = vec4(avg/2.0, avg/2.0, avg/2.0, previous_color.a);

	vec4 new_color = previous_color;

	if (active == true)
	{
		new_color = white_color;

		if (gray == true)
		{
			new_color = black_color;
		}
	}


	COLOR = new_color;
}"

[sub_resource type="ShaderMaterial" id=12]
resource_local_to_scene = true
shader = SubResource( 11 )
shader_param/active = false
shader_param/gray = false
shader_param/flash_color = Color( 1, 1, 1, 1 )

[sub_resource type="Gradient" id=58]
offsets = PoolRealArray( 0.002849, 0.381766, 0.666667 )
colors = PoolColorArray( 0.440329, 0.827778, 0.997559, 1, 0, 0.976562, 1, 1, 0, 1, 0.227451, 0 )

[sub_resource type="GradientTexture" id=57]
gradient = SubResource( 58 )

[sub_resource type="ParticlesMaterial" id=59]
emission_shape = 1
emission_sphere_radius = 10.65
flag_align_y = true
flag_rotate_y = true
flag_disable_z = true
direction = Vector3( 0, 0, 0 )
spread = 0.0
gravity = Vector3( 0, 0, 0 )
orbit_velocity = 0.0
orbit_velocity_random = 0.0
radial_accel = 100.0
radial_accel_random = 1.0
scale = 0.1
scale_random = 0.1
color_ramp = SubResource( 57 )

[sub_resource type="Gradient" id=8]
interpolation_mode = 2
colors = PoolColorArray( 0, 0.789062, 1, 1, 0, 0.0859375, 1, 0 )

[sub_resource type="GradientTexture" id=16]
gradient = SubResource( 8 )

[sub_resource type="ParticlesMaterial" id=9]
flag_disable_z = true
direction = Vector3( 0, 0, 0 )
gravity = Vector3( 0, 0, 0 )
initial_velocity = 15.0
initial_velocity_random = 1.0
orbit_velocity = 0.0
orbit_velocity_random = 0.0
tangential_accel = 12.0
tangential_accel_random = 1.0
scale = 0.15
scale_random = 0.05
color_ramp = SubResource( 16 )

[sub_resource type="CapsuleShape2D" id=10]
radius = 23.6364

[node name="SpiderBoss" type="Area2D"]
scale = Vector2( 2.2, 2.2 )
collision_layer = 4
collision_mask = 35
script = ExtResource( 8 )

[node name="HugeCristalBase" type="Sprite" parent="."]
position = Vector2( 0, -57 )
scale = Vector2( 0.205513, 0.205513 )
texture = ExtResource( 1 )

[node name="Beam" parent="." instance=ExtResource( 9 )]
visible = false
position = Vector2( 0, 19.5455 )

[node name="BeamParticles" type="Particles2D" parent="."]
visible = false
position = Vector2( -0.454545, 44.0909 )
scale = Vector2( 0.205513, 0.205513 )
amount = 20
lifetime = 0.3
process_material = SubResource( 15 )
texture = ExtResource( 10 )

[node name="LeftLegs" type="Node2D" parent="."]

[node name="SpiderLeg1" parent="LeftLegs" instance=ExtResource( 2 )]
position = Vector2( -23, -24 )
rotation = 0.754515
EnergySource = NodePath("../../ShieldBalls/ShieldEnergyBall")
EnergySourceBeam = NodePath("../../ShieldBalls/Energy/SpiderEnergyShieldBeam")

[node name="SpiderLeg2" parent="LeftLegs" instance=ExtResource( 2 )]
position = Vector2( -23, -15 )
rotation = 0.261799
EnergySource = NodePath("../../ShieldBalls/ShieldEnergyBall2")
EnergySourceBeam = NodePath("../../ShieldBalls/Energy/SpiderEnergyShieldBeam2")

[node name="SpiderLeg3" parent="LeftLegs" instance=ExtResource( 2 )]
position = Vector2( -29, 0 )
rotation = -0.261799
EnergySource = NodePath("../../ShieldBalls/ShieldEnergyBall3")
EnergySourceBeam = NodePath("../../ShieldBalls/Energy/SpiderEnergyShieldBeam3")

[node name="SpiderLeg4" parent="LeftLegs" instance=ExtResource( 2 )]
position = Vector2( -21, 15 )
rotation = -0.496263
scale = Vector2( 0.8, 0.8 )
EnergySource = NodePath("../../ShieldBalls/ShieldEnergyBall4")
EnergySourceBeam = NodePath("../../ShieldBalls/Energy/SpiderEnergyShieldBeam4")

[node name="RightLegs" type="Node2D" parent="."]
rotation = -3.14159
scale = Vector2( 1, -1 )

[node name="SpiderLeg1" parent="RightLegs" instance=ExtResource( 2 )]
position = Vector2( -23.093, -24.0888 )
rotation = 0.797082
EnergySource = NodePath("../../ShieldBalls/ShieldEnergyBall5")
EnergySourceBeam = NodePath("../../ShieldBalls/Energy/SpiderEnergyShieldBeam5")

[node name="SpiderLeg2" parent="RightLegs" instance=ExtResource( 2 )]
position = Vector2( -22.9667, -15.1242 )
rotation = 0.261799
EnergySource = NodePath("../../ShieldBalls/ShieldEnergyBall6")
EnergySourceBeam = NodePath("../../ShieldBalls/Energy/SpiderEnergyShieldBeam6")

[node name="SpiderLeg3" parent="RightLegs" instance=ExtResource( 2 )]
position = Vector2( -28.8758, -0.0332718 )
rotation = -0.261799
EnergySource = NodePath("../../ShieldBalls/ShieldEnergyBall7")
EnergySourceBeam = NodePath("../../ShieldBalls/Energy/SpiderEnergyShieldBeam7")

[node name="SpiderLeg4" parent="RightLegs" instance=ExtResource( 2 )]
position = Vector2( -20.8729, 15.0194 )
rotation = -0.468362
scale = Vector2( 0.8, 0.8 )
EnergySource = NodePath("../../ShieldBalls/ShieldEnergyBall8")
EnergySourceBeam = NodePath("../../ShieldBalls/Energy/SpiderEnergyShieldBeam8")

[node name="Body" type="Sprite" parent="."]
material = SubResource( 12 )
position = Vector2( 0, 4 )
texture = ExtResource( 3 )

[node name="Cracks" type="Node2D" parent="Body"]
modulate = Color( 1, 1, 1, 0 )

[node name="Crack" type="Sprite" parent="Body/Cracks"]
modulate = Color( 0, 0, 0, 1 )
position = Vector2( 10.4545, -2.18182 )
scale = Vector2( 0.0528858, 0.101236 )
texture = ExtResource( 11 )

[node name="Particles2D" type="Particles2D" parent="Body/Cracks/Crack"]
modulate = Color( 1, 1, 1, 0.745098 )
position = Vector2( -48.9271, -64.408 )
amount = 15
lifetime = 0.5
local_coords = false
process_material = SubResource( 59 )
texture = ExtResource( 12 )

[node name="Crack2" type="Sprite" parent="Body/Cracks"]
modulate = Color( 0, 0, 0, 1 )
position = Vector2( -0.454544, -21.7273 )
rotation = 1.0472
scale = Vector2( 0.0846932, 0.0866946 )
texture = ExtResource( 11 )

[node name="Particles2D" type="Particles2D" parent="Body/Cracks/Crack2"]
modulate = Color( 1, 1, 1, 0.745098 )
position = Vector2( -48.9271, -64.408 )
amount = 15
lifetime = 0.5
local_coords = false
process_material = SubResource( 59 )
texture = ExtResource( 12 )

[node name="Crack3" type="Sprite" parent="Body/Cracks"]
modulate = Color( 0, 0, 0, 1 )
position = Vector2( -10.9091, -1.72728 )
rotation = 1.0472
scale = Vector2( 0.0398211, 0.0421928 )
texture = ExtResource( 11 )

[node name="Particles2D" type="Particles2D" parent="Body/Cracks/Crack3"]
modulate = Color( 1, 1, 1, 0.745098 )
position = Vector2( -48.9271, -64.408 )
amount = 15
lifetime = 0.5
local_coords = false
process_material = SubResource( 59 )
texture = ExtResource( 12 )

[node name="ShieldBalls" type="Node2D" parent="."]
modulate = Color( 1, 1, 1, 0.427451 )

[node name="ShieldEnergyBall" parent="ShieldBalls" instance=ExtResource( 4 )]
position = Vector2( -23, -24 )

[node name="ShieldEnergyBall2" parent="ShieldBalls" instance=ExtResource( 4 )]
position = Vector2( -23, -15 )

[node name="ShieldEnergyBall3" parent="ShieldBalls" instance=ExtResource( 4 )]
position = Vector2( -29, -0.5 )

[node name="ShieldEnergyBall4" parent="ShieldBalls" instance=ExtResource( 4 )]
position = Vector2( -21, 15 )

[node name="ShieldEnergyBall5" parent="ShieldBalls" instance=ExtResource( 4 )]
position = Vector2( 23, -24 )

[node name="ShieldEnergyBall6" parent="ShieldBalls" instance=ExtResource( 4 )]
position = Vector2( 23, -15.5 )

[node name="ShieldEnergyBall7" parent="ShieldBalls" instance=ExtResource( 4 )]
position = Vector2( 29, 0 )

[node name="ShieldEnergyBall8" parent="ShieldBalls" instance=ExtResource( 4 )]
position = Vector2( 21, 15 )

[node name="ShieldEnergyBallCenter" parent="ShieldBalls" instance=ExtResource( 4 )]
position = Vector2( 0, -0.5 )
scale = Vector2( 2, 2 )

[node name="Energy" type="Node2D" parent="ShieldBalls"]
modulate = Color( 1, 1, 1, 0.54902 )

[node name="SpiderEnergyShieldBeam" parent="ShieldBalls/Energy" instance=ExtResource( 5 )]
position = Vector2( -23, -24 )
rotation = -0.785397

[node name="SpiderEnergyShieldBeam2" parent="ShieldBalls/Energy" instance=ExtResource( 5 )]
position = Vector2( -23, -15 )
rotation = -1.0472

[node name="SpiderEnergyShieldBeam3" parent="ShieldBalls/Energy" instance=ExtResource( 5 )]
position = Vector2( -29, -0.5 )
rotation = -1.59791

[node name="SpiderEnergyShieldBeam4" parent="ShieldBalls/Energy" instance=ExtResource( 5 )]
position = Vector2( -21, 15 )
rotation = -2.23835

[node name="SpiderEnergyShieldBeam5" parent="ShieldBalls/Energy" instance=ExtResource( 5 )]
position = Vector2( 23, -24 )
rotation = 0.813037

[node name="SpiderEnergyShieldBeam6" parent="ShieldBalls/Energy" instance=ExtResource( 5 )]
position = Vector2( 23, -15 )
rotation = 1.02042

[node name="SpiderEnergyShieldBeam7" parent="ShieldBalls/Energy" instance=ExtResource( 5 )]
position = Vector2( 29, 0 )
rotation = 1.59119

[node name="SpiderEnergyShieldBeam8" parent="ShieldBalls/Energy" instance=ExtResource( 5 )]
position = Vector2( 21, 15 )
rotation = 2.21836

[node name="FrontShield" type="Node2D" parent="."]

[node name="SpiderEnergyShieldBeam" parent="FrontShield" instance=ExtResource( 7 )]

[node name="ShieldEffect" type="Particles2D" parent="FrontShield"]
position = Vector2( -0.454545, 11.3636 )
process_material = SubResource( 9 )
texture = ExtResource( 6 )

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
visible = false
position = Vector2( 0, -5.45455 )
shape = SubResource( 10 )
