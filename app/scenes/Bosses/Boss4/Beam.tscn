[gd_scene load_steps=15 format=2]

[ext_resource path="res://assets/SpiderBoss/beam.png" type="Texture" id=1]
[ext_resource path="res://addons/kenney_particle_pack/16/trace_06_16.png" type="Texture" id=2]
[ext_resource path="res://addons/kenney_particle_pack/16/light_01_16.png" type="Texture" id=3]
[ext_resource path="res://addons/kenney_particle_pack/spark_04.png" type="Texture" id=4]
[ext_resource path="res://scenes/Bosses/Boss4/Beam.gd" type="Script" id=5]
[ext_resource path="res://addons/kenney_particle_pack/spark_05.png" type="Texture" id=6]

[sub_resource type="RectangleShape2D" id=1]
extents = Vector2( 15.5, 218 )

[sub_resource type="ParticlesMaterial" id=3]
emission_shape = 2
emission_box_extents = Vector3( 13, 1, 1 )
flag_disable_z = true
direction = Vector3( 0, 1, 0 )
spread = 0.0
gravity = Vector3( 0, 0, 0 )
initial_velocity = 400.0
initial_velocity_random = 0.5
orbit_velocity = 0.0
orbit_velocity_random = 0.0
scale = 2.0
scale_random = 1.0
color = Color( 0.992157, 1, 0, 0.368627 )

[sub_resource type="ParticlesMaterial" id=2]
emission_shape = 2
emission_box_extents = Vector3( 13, 1, 1 )
flag_disable_z = true
direction = Vector3( 0, 1, 0 )
spread = 0.0
gravity = Vector3( 0, 0, 0 )
initial_velocity = 400.0
initial_velocity_random = 0.5
orbit_velocity = 0.0
orbit_velocity_random = 0.0
scale = 2.0
scale_random = 1.0
color = Color( 0, 0, 0, 0.309804 )

[sub_resource type="Gradient" id=58]
offsets = PoolRealArray( 0.002849, 0.382289, 0.666667 )
colors = PoolColorArray( 0.440329, 0.827778, 0.997559, 1, 1, 1, 1, 1, 0, 1, 0.227451, 0 )

[sub_resource type="GradientTexture" id=57]
gradient = SubResource( 58 )

[sub_resource type="ParticlesMaterial" id=61]
lifetime_randomness = 0.5
emission_shape = 2
emission_box_extents = Vector3( 12, 130, 1 )
direction = Vector3( 0, -1, 0 )
spread = 0.0
gravity = Vector3( 0, 0, 0 )
initial_velocity = 100.0
initial_velocity_random = 1.0
scale = 0.15
scale_random = 0.05
color_ramp = SubResource( 57 )

[sub_resource type="ParticlesMaterial" id=59]
lifetime_randomness = 0.5
emission_shape = 2
emission_box_extents = Vector3( 12, 130, 1 )
direction = Vector3( 0, -1, 0 )
spread = 0.0
gravity = Vector3( 0, 0, 0 )
initial_velocity = 100.0
initial_velocity_random = 1.0
scale = 0.1
scale_random = 0.05
color_ramp = SubResource( 57 )

[sub_resource type="Animation" id=60]
resource_name = "BeamWidth"
length = 2.5
loop = true
tracks/0/type = "value"
tracks/0/path = NodePath("BeamLine:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 1.3, 2.5 ),
"transitions": PoolRealArray( 1, 0.5, 0.5 ),
"update": 0,
"values": [ Vector2( 1, 1 ), Vector2( 1.2, 1 ), Vector2( 1, 1 ) ]
}

[node name="Beam" type="Area2D"]
collision_layer = 2
collision_mask = 33
script = ExtResource( 5 )

[node name="BeamLine" type="Node2D" parent="."]
scale = Vector2( 1.00562, 1 )

[node name="Beam2" type="Sprite" parent="BeamLine"]
position = Vector2( 0.400001, 9.65 )
scale = Vector2( 2.2, 6.43333 )
texture = ExtResource( 1 )

[node name="Beam3" type="Sprite" parent="BeamLine"]
position = Vector2( 0.400001, 28.65 )
scale = Vector2( 2.2, 6.43333 )
texture = ExtResource( 1 )

[node name="Beam4" type="Sprite" parent="BeamLine"]
position = Vector2( 0.400001, 47.65 )
scale = Vector2( 2.2, 6.43333 )
texture = ExtResource( 1 )

[node name="Beam5" type="Sprite" parent="BeamLine"]
position = Vector2( 0.400001, 66.65 )
scale = Vector2( 2.2, 6.43333 )
texture = ExtResource( 1 )

[node name="Beam6" type="Sprite" parent="BeamLine"]
position = Vector2( 0.400001, 85.65 )
scale = Vector2( 2.2, 6.43333 )
texture = ExtResource( 1 )

[node name="Beam7" type="Sprite" parent="BeamLine"]
position = Vector2( 0.400001, 104.65 )
scale = Vector2( 2.2, 6.43333 )
texture = ExtResource( 1 )

[node name="Beam8" type="Sprite" parent="BeamLine"]
position = Vector2( 0.400001, 123.65 )
scale = Vector2( 2.2, 6.43333 )
texture = ExtResource( 1 )

[node name="Beam9" type="Sprite" parent="BeamLine"]
position = Vector2( 0.400001, 142.65 )
scale = Vector2( 2.2, 6.43333 )
texture = ExtResource( 1 )

[node name="Beam10" type="Sprite" parent="BeamLine"]
position = Vector2( 0.400001, 161.65 )
scale = Vector2( 2.2, 6.43333 )
texture = ExtResource( 1 )

[node name="Beam11" type="Sprite" parent="BeamLine"]
position = Vector2( 0.400001, 180.65 )
scale = Vector2( 2.2, 6.43333 )
texture = ExtResource( 1 )

[node name="Beam12" type="Sprite" parent="BeamLine"]
position = Vector2( 0.400001, 199.65 )
scale = Vector2( 2.2, 6.43333 )
texture = ExtResource( 1 )

[node name="Beam" type="Sprite" parent="BeamLine"]
position = Vector2( 0.400001, 218.3 )
scale = Vector2( 2.2, 6.43333 )
texture = ExtResource( 1 )

[node name="Beam13" type="Sprite" parent="BeamLine"]
position = Vector2( 0.400001, 237.3 )
scale = Vector2( 2.2, 6.43333 )
texture = ExtResource( 1 )

[node name="Beam14" type="Sprite" parent="BeamLine"]
position = Vector2( 0.400001, 256.3 )
scale = Vector2( 2.2, 6.43333 )
texture = ExtResource( 1 )

[node name="Beam24" type="Sprite" parent="BeamLine"]
position = Vector2( 0.400001, 218.3 )
scale = Vector2( 2.2, 6.43333 )
texture = ExtResource( 1 )

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
position = Vector2( 0.5, 219 )
shape = SubResource( 1 )

[node name="Particles2D2" type="Particles2D" parent="."]
amount = 100
lifetime = 0.6
process_material = SubResource( 3 )
texture = ExtResource( 3 )

[node name="Particles2D" type="Particles2D" parent="."]
amount = 50
lifetime = 0.8
process_material = SubResource( 2 )
texture = ExtResource( 2 )

[node name="Particles2D4" type="Particles2D" parent="."]
modulate = Color( 1, 1, 1, 0.588235 )
position = Vector2( -2, 146 )
amount = 50
lifetime = 0.5
process_material = SubResource( 61 )
texture = ExtResource( 4 )

[node name="Particles2D3" type="Particles2D" parent="."]
modulate = Color( 1, 1, 1, 0.501961 )
position = Vector2( 6, 141 )
amount = 20
lifetime = 0.5
process_material = SubResource( 59 )
texture = ExtResource( 6 )

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
autoplay = "BeamWidth"
playback_speed = 2.0
anims/BeamWidth = SubResource( 60 )
