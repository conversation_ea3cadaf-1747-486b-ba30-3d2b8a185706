extends Area2D

var brick_min = 0;
var brick_max = 0;
var brick_size = 10;
var isActive = false

var state = Global.EnemyMode.INIT

var Explosion = preload("res://scenes/Explosion_1.tscn")
var Bullet = preload("res://scenes/EnemyBullet_1.tscn")
var	Bullet2 = preload("res://scenes/EnemyBullet_2.tscn")

func _ready():

	Global.GameScene.wasDiscoBallDestoryed = Global.DiscoBallState.MERCY

	# set min max for nodes
	var nodes = $Bricks/ScrollingBricks.get_children()
	for node in nodes:
		if node.position.x <brick_min:
			brick_min = node.position.x
		if node.position.x > brick_max:
			brick_max = node.position.x
		isActive = true

	var _c = connect("area_entered",self,"_on_hit")

func removeAllFromScene():
	queue_free()

func removeFromScene():
	state = Global.EnemyMode.DEAD

	var tween = Global.createTween(self)
	tween.interpolate_property(self,"position:y",position.y,-300.0,0.5, Tween.TRANS_QUAD, Tween.EASE_OUT)
	tween.interpolate_property(self,"scale",scale,scale-Vector2(0.5,0.5),1, Tween.TRANS_EXPO, Tween.EASE_OUT)
	# tween.interpolate_property(self,"modulate:a",1,0,1, Tween.TRANS_QUAD, Tween.EASE_OUT)
	tween.connect("tween_all_completed",self,"removeAllFromScene")
	tween.start()

func explosion():
	var boss = $AnimatedSprite
	var explosion = Explosion.instance()
	explosion.position = boss.global_position + Vector2(randi()%50-50,randi()%50-50);
	explosion.z_index  = z_index+1;
	explosion.scale = Vector2(2,2)
	Global.GameScene.add_child(explosion);

func bossKilled():

	for _i in range(0,10):
		Global.setTimeout(self,randf()/2,self,"explosion")

	var boss = $AnimatedSprite
	var tween = Global.createTween(boss)
	tween.interpolate_property(boss,"scale",boss.scale,boss.scale+Vector2(1,1),2, Tween.TRANS_EXPO, Tween.EASE_OUT)
	tween.interpolate_property(boss,"modulate:a",1,0,2, Tween.TRANS_QUAD, Tween.EASE_OUT)
	tween.connect("tween_all_completed",self,"removeFromScene")
	tween.start()


var isKilled = false

func _on_hit(area):
	if(Global.GameScene.isCurrentBossReady && area.has_method("getDamagePoint") && !isKilled && canBossBeKilled):
		Global.callIfExists(area,"destroy")
		# area.destroy()
		isKilled = true
		if is_instance_valid($DiscoBall):
			$DiscoBall.isBossAlive = false
		bossKilled()

func getBricks():
	return $Bricks/DescruptibleBricks.get_children();

func getScrollingBricks():
	return $Bricks/ScrollingBricks.get_children();

func getBoss():
	return $AnimatedSprite;

func getDiscoBall():
	# SAFETY: Check if DiscoBall exists before returning it
	if has_node("DiscoBall"):
		return $DiscoBall
	else:
		return null

func moveBricks():
	var nodes = $Bricks/ScrollingBricks.get_children()
	for node in nodes:
		node.position.x -= brick_size
		if node.position.x < brick_min:
			node.position.x = brick_max

var bulletCnt = 0
var shipWidth = 350
var anger = 0

func handleBulletRain():

	if(state==Global.EnemyMode.DEAD or isKilled):
		return false

	if(Global.GameScene.isPlayerReady() == false):
		return false

	if(!Global.doThrottle("boss1bullet", max(400,1000-anger-Global.GameScene.getProperDifficulty()*100))):

		if anger<=0:
			# NOOOO my disco ball :)
			if (!is_instance_valid($DiscoBall)):
				Global.GameScene.spawnBonusLabel(position, "Nooooooo!!!\nNot MY disco ball !!!", 0.3, true,false,0.8);
				Global.playTts(SoundManager.tts_weeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee)
				anger = 500
				$AnimatedSprite.speed_scale = 10

		# evenry 3rd bullet is precise
		var doFireRandom = (not bulletCnt%3==0)
		var doFireAtPlayer = false 

		if(Global.getPlayer().position.x < position.x-shipWidth/2 or Global.getPlayer().position.x > position.x+shipWidth/2):
			doFireRandom = true
			doFireAtPlayer = Global.isChance(randi(),30) 

		var bullet = Bullet.instance()
		bullet.position.y = position.y+120

		if doFireRandom:
			bullet.position.x = (position.x-(shipWidth/2)) + randi()%shipWidth
		else:
			bullet.position.x = Global.getPlayer().position.x-50 + randi()%100

		bullet.z_index  = z_index-1;
		Global.GameScene.add_child(bullet);
		bulletCnt+=1

		if doFireAtPlayer:
			if is_instance_valid($DiscoBall):
				var bullet2 = Bullet2.instance()
				bullet2.position = $DiscoBall.global_position
				bullet2.z_index  = z_index-1;
				Global.GameScene.add_child(bullet2);

var destPosition = Vector2(0,0)

func move(delta):

	var speed = 10
	var diff = destPosition - position

	if(diff.abs()>Vector2(10,10)):
		var velocity = (diff/Vector2(10,10) * speed)
		position += velocity * delta

	if Global.doThrottle("boss1move", 1000):
		return false

	if Global.isChance(randi(),5):
		destPosition.x = shipWidth + randi()%int(Global.getWindowSize().x-shipWidth*2)
		destPosition.y = (Global.getWindowSize().y / 4)-80+randi()%100;


func _process(delta):

	if !isActive:
		return false

	if !Global.doThrottle("boss1brickscroll", 500):
		moveBricks()
	
	if state == Global.EnemyMode.ATTACK:
		handleBulletRain()
		move(delta)


func init():
	position.y = -200
	position.x = Global.getWindowSize().x / 2
	z_index = Config.TopZIndex

	# start entry
	var tween = Global.createTween(self)
	tween.interpolate_property(self,"position:y",position.y, (Global.getWindowSize().y / 4)-80, 2,Tween.TRANS_QUAD, Tween.EASE_OUT);
	tween.connect("tween_completed", self, "onEntryComplete")
	tween.start()

var canBossBeKilled = false

func setBossKillablility():
	canBossBeKilled = true

func onEntryComplete(_o,_e):
	destPosition = position
	state = Global.EnemyMode.ATTACK
	Global.GameScene.isCurrentBossReady = true
	Global.setTimeout(self,2,self,"setBossKillablility")

func isDead():
	return state==Global.EnemyMode.DEAD
