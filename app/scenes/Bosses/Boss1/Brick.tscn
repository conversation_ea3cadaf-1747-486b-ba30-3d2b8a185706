[gd_scene load_steps=14 format=2]

[ext_resource path="res://assets/boss_1_elements.png" type="Texture" id=1]
[ext_resource path="res://scenes/Bosses/Boss1/Brick.gd" type="Script" id=2]

[sub_resource type="AtlasTexture" id=1]
atlas = ExtResource( 1 )
region = Rect2( 0, 0, 10, 10 )

[sub_resource type="AtlasTexture" id=2]
atlas = ExtResource( 1 )
region = Rect2( 10, 0, 10, 10 )

[sub_resource type="AtlasTexture" id=3]
atlas = ExtResource( 1 )
region = Rect2( 20, 0, 10, 10 )

[sub_resource type="AtlasTexture" id=4]
atlas = ExtResource( 1 )
region = Rect2( 30, 0, 10, 10 )

[sub_resource type="AtlasTexture" id=5]
atlas = ExtResource( 1 )
region = Rect2( 40, 0, 10, 10 )

[sub_resource type="AtlasTexture" id=6]
atlas = ExtResource( 1 )
region = Rect2( 50, 0, 10, 10 )

[sub_resource type="SpriteFrames" id=7]
animations = [ {
"frames": [ SubResource( 1 ), SubResource( 2 ), SubResource( 3 ), SubResource( 4 ), SubResource( 5 ), SubResource( 6 ) ],
"loop": true,
"name": "default",
"speed": 5.0
} ]

[sub_resource type="RectangleShape2D" id=8]
extents = Vector2( 5, 5 )

[sub_resource type="Gradient" id=10]
offsets = PoolRealArray( 0.810127, 1 )
colors = PoolColorArray( 0.149497, 0.589355, 0.147339, 1, 1, 1, 1, 0 )

[sub_resource type="GradientTexture" id=11]
gradient = SubResource( 10 )

[sub_resource type="ParticlesMaterial" id=9]
resource_local_to_scene = true
emission_shape = 2
emission_box_extents = Vector3( 4, 4, 4 )
flag_disable_z = true
direction = Vector3( 0, -1, 0 )
spread = 30.0
gravity = Vector3( 0, 98, 0 )
initial_velocity = 10.0
orbit_velocity = 0.0
orbit_velocity_random = 0.0
linear_accel = 10.0
linear_accel_random = 1.0
damping = 50.0
damping_random = 1.0
scale = 2.0
scale_random = 1.0
color_ramp = SubResource( 11 )

[node name="Brick" type="Area2D"]
collision_layer = 4
collision_mask = 34
script = ExtResource( 2 )

[node name="AnimatedSprite" type="AnimatedSprite" parent="."]
frames = SubResource( 7 )
frame = 4

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
visible = false
shape = SubResource( 8 )

[node name="Explosion" type="Particles2D" parent="."]
modulate = Color( 1, 1, 1, 0.847059 )
emitting = false
amount = 20
one_shot = true
speed_scale = 2.0
explosiveness = 0.87
process_material = SubResource( 9 )
