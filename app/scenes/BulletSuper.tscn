[gd_scene load_steps=8 format=2]

[ext_resource path="res://assets/bullet_ball.png" type="Texture" id=1]
[ext_resource path="res://addons/kenney_particle_pack/16/slash_02_16.png" type="Texture" id=2]
[ext_resource path="res://scripts/BulletSuper.gd" type="Script" id=3]

[sub_resource type="Gradient" id=1]
colors = PoolColorArray( 0, 1, 0.694118, 0.631373, 1, 1, 1, 0 )

[sub_resource type="GradientTexture" id=2]
gradient = SubResource( 1 )

[sub_resource type="ParticlesMaterial" id=3]
flag_disable_z = true
direction = Vector3( 0, 1, 0 )
spread = 0.0
gravity = Vector3( 0, 0, 0 )
initial_velocity = 100.0
angular_velocity = 500.0
angular_velocity_random = 1.0
orbit_velocity = 0.0
orbit_velocity_random = 0.0
scale = 1.3
scale_random = 1.0
color_ramp = SubResource( 2 )

[sub_resource type="CircleShape2D" id=4]
radius = 5.0

[node name="BulletSuper" type="Area2D"]
collision_layer = 2
collision_mask = 44
script = ExtResource( 3 )

[node name="LightParticle" type="Particles2D" parent="."]
amount = 10
lifetime = 0.5
speed_scale = 2.0
process_material = SubResource( 3 )
texture = ExtResource( 2 )

[node name="Sprite" type="Sprite" parent="."]
modulate = Color( 0, 1, 1, 1 )
texture = ExtResource( 1 )

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource( 4 )
