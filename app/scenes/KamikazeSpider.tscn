[gd_scene load_steps=14 format=2]

[ext_resource path="res://scripts/KamikazeSpider.gd" type="Script" id=1]
[ext_resource path="res://assets/SpiderBoss/spider-minion.png" type="Texture" id=2]
[ext_resource path="res://addons/kenney_particle_pack/16/scratch_01_16.png" type="Texture" id=3]
[ext_resource path="res://assets/SpiderBoss/spider-minion2.png" type="Texture" id=4]
[ext_resource path="res://addons/kenney_particle_pack/circle_04.png" type="Texture" id=5]

[sub_resource type="Curve" id=26]
_data = [ Vector2( 0, 1 ), 0.0, 0.0, 0, 0, Vector2( 1, 0 ), 0.0, 0.0, 0, 0 ]

[sub_resource type="CurveTexture" id=25]
curve = SubResource( 26 )

[sub_resource type="ParticlesMaterial" id=27]
flag_disable_z = true
gravity = Vector3( 0, 0, 0 )
angular_velocity = 200.0
angular_velocity_random = 1.0
orbit_velocity = 0.0
orbit_velocity_random = 0.0
scale = 2.0
scale_random = 1.0
scale_curve = SubResource( 25 )

[sub_resource type="SpriteFrames" id=28]
animations = [ {
"frames": [ ExtResource( 2 ), ExtResource( 4 ) ],
"loop": true,
"name": "default",
"speed": 2.0
} ]

[sub_resource type="CircleShape2D" id=1]

[sub_resource type="Gradient" id=29]
interpolation_mode = 2
colors = PoolColorArray( 0, 0.789062, 1, 1, 0, 0.0859375, 1, 0 )

[sub_resource type="GradientTexture" id=7]
gradient = SubResource( 29 )

[sub_resource type="ParticlesMaterial" id=30]
flag_disable_z = true
direction = Vector3( 0, 0, 0 )
gravity = Vector3( 0, 0, 0 )
initial_velocity = 15.0
initial_velocity_random = 1.0
orbit_velocity = 0.0
orbit_velocity_random = 0.0
tangential_accel = 12.0
tangential_accel_random = 1.0
scale = 0.1
scale_random = 0.01
color_ramp = SubResource( 7 )

[node name="Area2D" type="Area2D"]
collision_layer = 12
collision_mask = 3
script = ExtResource( 1 )

[node name="Particles2D" type="Particles2D" parent="."]
modulate = Color( 0.552941, 0.556863, 0.0470588, 1 )
position = Vector2( -1.6496e-06, -3.6642e-12 )
rotation = -1.57079
scale = Vector2( 3.36842, 3.45946 )
lifetime = 0.3
local_coords = false
process_material = SubResource( 27 )
texture = ExtResource( 3 )

[node name="AnimatedSprite" type="AnimatedSprite" parent="."]
rotation = -1.57094
scale = Vector2( 1.5, 1.5 )
frames = SubResource( 28 )
playing = true

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource( 1 )

[node name="ShieldEffect" type="Particles2D" parent="."]
modulate = Color( 1, 1, 1, 0.290196 )
process_material = SubResource( 30 )
texture = ExtResource( 5 )
