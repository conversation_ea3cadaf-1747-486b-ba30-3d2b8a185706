[gd_scene load_steps=6 format=2]

[ext_resource path="res://assets/enemy1_2.png" type="Texture" id=1]
[ext_resource path="res://assets/enemy1_1.png" type="Texture" id=2]
[ext_resource path="res://scripts/Enemy_1.gd" type="Script" id=3]

[sub_resource type="SpriteFrames" id=1]
animations = [ {
"frames": [ ExtResource( 2 ), ExtResource( 1 ) ],
"loop": true,
"name": "default",
"speed": 1.0
} ]

[sub_resource type="RectangleShape2D" id=2]
extents = Vector2( 14.6667, 14.6667 )

[node name="Enemy_1" type="Area2D"]
collision_layer = 4
collision_mask = 3
script = ExtResource( 3 )

[node name="AnimatedSprite" type="AnimatedSprite" parent="."]
scale = Vector2( 2.75, 2.75 )
frames = SubResource( 1 )
playing = true

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
visible = false
position = Vector2( -7.15256e-07, 7.15256e-07 )
scale = Vector2( 1.5, 1.5 )
shape = SubResource( 2 )
