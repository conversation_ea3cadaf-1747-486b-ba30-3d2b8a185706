[gd_scene load_steps=7 format=2]

[ext_resource path="res://shaders/hit.tres" type="Shader" id=1]
[ext_resource path="res://assets/huge_cristal_empty.png" type="Texture" id=2]
[ext_resource path="res://assets/huge_cristal_base.png" type="Texture" id=3]
[ext_resource path="res://scripts/LargeCrystal.gd" type="Script" id=4]

[sub_resource type="ShaderMaterial" id=1]
resource_local_to_scene = true
shader = ExtResource( 1 )
shader_param/active = false
shader_param/gray = false
shader_param/flash_color = Color( 1, 1, 1, 1 )

[sub_resource type="ShaderMaterial" id=2]
resource_local_to_scene = true
shader = ExtResource( 1 )
shader_param/active = false
shader_param/gray = false
shader_param/flash_color = Color( 1, 1, 1, 1 )

[node name="LargeCrystal" type="Area2D"]
position = Vector2( 2, 0 )
scale = Vector2( 0.5, 0.5 )
collision_layer = 52
collision_mask = 2
script = ExtResource( 4 )

[node name="HugeCrystalEmpty" type="Sprite" parent="."]
material = SubResource( 1 )
texture = ExtResource( 2 )

[node name="HugeCrystalBase" type="Sprite" parent="."]
material = SubResource( 2 )
position = Vector2( 3, -1 )
texture = ExtResource( 3 )

[node name="CollisionPolygon2D" type="CollisionPolygon2D" parent="."]
visible = false
polygon = PoolVector2Array( 106.5, -91.5, 106.5, 124, 104, 124, 17, 260, 11.5, 260, -16.5, 266.6, -16.5, 255.9, -106.5, 115, -106.5, -84.5, -10.5, -253.6, -10.5, -259.6, 4.2, -263.7 )
