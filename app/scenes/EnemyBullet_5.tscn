[gd_scene load_steps=11 format=2]

[ext_resource path="res://assets/bullet_ball.png" type="Texture" id=1]
[ext_resource path="res://scripts/EnemyBullet_5.gd" type="Script" id=2]
[ext_resource path="res://assets/sounds/laser1.wav" type="AudioStream" id=3]
[ext_resource path="res://addons/kenney_particle_pack/16/scratch_01_16.png" type="Texture" id=4]

[sub_resource type="Gradient" id=22]
colors = PoolColorArray( 0.172549, 1, 0, 0.368627, 1, 1, 1, 0 )

[sub_resource type="GradientTexture2D" id=20]
gradient = SubResource( 22 )
fill = 1
fill_from = Vector2( 0.5, 0.5 )
fill_to = Vector2( 0.5, 0 )

[sub_resource type="Curve" id=24]
_data = [ Vector2( 0, 1 ), 0.0, 0.0, 0, 0, Vector2( 1, 0 ), 0.0, 0.0, 0, 0 ]

[sub_resource type="CurveTexture" id=25]
curve = SubResource( 24 )

[sub_resource type="ParticlesMaterial" id=23]
flag_disable_z = true
gravity = Vector3( 0, 0, 0 )
angular_velocity = 200.0
angular_velocity_random = 1.0
orbit_velocity = 0.0
orbit_velocity_random = 0.0
scale = 1.3
scale_random = 1.0
scale_curve = SubResource( 25 )

[sub_resource type="CircleShape2D" id=19]
radius = 4.0

[node name="EnemyBullet_5" type="Area2D"]
collision_layer = 8
collision_mask = 33
script = ExtResource( 2 )

[node name="Sprite" type="Sprite" parent="."]
modulate = Color( 0.921875, 1, 0, 1 )
position = Vector2( 0, 4.76837e-07 )
scale = Vector2( 0.296875, 0.289062 )
texture = SubResource( 20 )

[node name="Particles2D" type="Particles2D" parent="Sprite"]
position = Vector2( 0, -1.6496e-06 )
scale = Vector2( 3.36842, 3.45946 )
lifetime = 0.3
local_coords = false
process_material = SubResource( 23 )
texture = ExtResource( 4 )

[node name="BulletBall" type="Sprite" parent="."]
modulate = Color( 0, 1, 0, 1 )
position = Vector2( 0, 2.84217e-14 )
texture = ExtResource( 1 )

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource( 19 )

[node name="AudioStreamPlayer2D" type="AudioStreamPlayer2D" parent="."]
stream = ExtResource( 3 )
volume_db = -10.0
