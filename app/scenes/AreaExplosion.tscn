[gd_scene load_steps=7 format=2]

[ext_resource path="res://scripts/AreaExplosion.gd" type="Script" id=1]
[ext_resource path="res://addons/kenney_particle_pack/light_03.png" type="Texture" id=2]
[ext_resource path="res://scenes/Explosion_1.tscn" type="PackedScene" id=3]

[sub_resource type="CircleShape2D" id=1]
radius = 17.5735

[sub_resource type="Animation" id=2]
resource_name = "AreaExplosion"
tracks/0/type = "value"
tracks/0/path = NodePath(".:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.5 ),
"transitions": PoolRealArray( 1, 0.5 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 0 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:scale")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.1, 0.5 ),
"transitions": PoolRealArray( 1, 0.5, 2 ),
"update": 0,
"values": [ Vector2( 0.1, 0.1 ), Vector2( 0.8, 0.6 ), Vector2( 0.5, 0.5 ) ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("../CollisionShape2D:scale")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 0.1, 0.5 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 1, 1 ), Vector2( 7, 6 ), Vector2( 0, 0 ) ]
}
tracks/3/type = "value"
tracks/3/path = NodePath("../CollisionShape2D:disabled")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0, 0.5, 1 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 1,
"values": [ false, true, true ]
}

[sub_resource type="Animation" id=3]
length = 0.001
tracks/0/type = "value"
tracks/0/path = NodePath("Light03:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Vector2( 1, 0 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("Light03:rotation_degrees")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ 0.0 ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("Light03:scale")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Vector2( 0.307617, 0.307617 ) ]
}

[node name="AreaExplosion" type="Area2D"]
scale = Vector2( 1.03101, 0.996825 )
collision_layer = 10
collision_mask = 37
script = ExtResource( 1 )

[node name="Explosion_1" parent="." instance=ExtResource( 3 )]
scale = Vector2( 3, 3 )

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
scale = Vector2( 1e-05, 1e-05 )
shape = SubResource( 1 )
disabled = true

[node name="Light03" type="Sprite" parent="."]
modulate = Color( 1, 1, 1, 0 )
position = Vector2( -3.25079, 0 )
scale = Vector2( 0.5, 0.5 )
texture = ExtResource( 2 )

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
root_node = NodePath("../Light03")
playback_speed = 2.0
anims/AreaExplosion = SubResource( 2 )
anims/RESET = SubResource( 3 )
