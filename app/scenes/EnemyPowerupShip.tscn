[gd_scene load_steps=12 format=2]

[ext_resource path="res://assets/Enemy - Ship - Frame 2.png" type="Texture" id=1]
[ext_resource path="res://assets/Enemy - Ship - Frame 3.png" type="Texture" id=2]
[ext_resource path="res://assets/Enemy - Ship - Frame 1.png" type="Texture" id=3]
[ext_resource path="res://addons/kenney_particle_pack/16/circle_04_16.png" type="Texture" id=4]
[ext_resource path="res://scripts/EnemyPowerupShip.gd" type="Script" id=5]

[sub_resource type="Curve" id=3]
_data = [ Vector2( 0, 1 ), 0.0, 0.0, 0, 0, Vector2( 1, 0.295455 ), 0.0, 0.0, 0, 0 ]

[sub_resource type="CurveTexture" id=4]
curve = SubResource( 3 )

[sub_resource type="ParticlesMaterial" id=5]
flag_disable_z = true
gravity = Vector3( 0, -98, 0 )
orbit_velocity = 0.0
orbit_velocity_random = 0.0
scale_curve = SubResource( 4 )

[sub_resource type="SpriteFrames" id=1]
animations = [ {
"frames": [ ExtResource( 3 ), ExtResource( 1 ), ExtResource( 2 ) ],
"loop": true,
"name": "default",
"speed": 5.0
} ]

[sub_resource type="CapsuleShape2D" id=2]
radius = 29.0
height = 6.0

[sub_resource type="Animation" id=6]
resource_name = "PowerupShipAnimation"
length = 2.0
loop = true
tracks/0/type = "value"
tracks/0/path = NodePath("AnimatedSprite:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 1, 2 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 0.6, 0.6, 0.6, 1 ), Color( 1, 1, 1, 1 ) ]
}

[node name="Area2D" type="Area2D"]
collision_layer = 36
collision_mask = 3
script = ExtResource( 5 )

[node name="Particles2D" type="Particles2D" parent="."]
position = Vector2( -26, -17 )
amount = 4
local_coords = false
process_material = SubResource( 5 )
texture = ExtResource( 4 )

[node name="Particles2D2" type="Particles2D" parent="."]
local_coords = false
process_material = SubResource( 5 )
texture = ExtResource( 4 )

[node name="Particles2D3" type="Particles2D" parent="."]
position = Vector2( 25, -17 )
amount = 4
local_coords = false
process_material = SubResource( 5 )
texture = ExtResource( 4 )

[node name="AnimatedSprite" type="AnimatedSprite" parent="."]
modulate = Color( 0.68, 0.68, 0.68, 1 )
scale = Vector2( 2, 2 )
frames = SubResource( 1 )

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
visible = false
shape = SubResource( 2 )

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
autoplay = "PowerupShipAnimation"
anims/PowerupShipAnimation = SubResource( 6 )
