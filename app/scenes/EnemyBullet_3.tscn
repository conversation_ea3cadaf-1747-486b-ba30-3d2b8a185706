[gd_scene load_steps=44 format=2]

[ext_resource path="res://scripts/EnemyBullet_3.gd" type="Script" id=2]
[ext_resource path="res://assets/sounds/laser1.wav" type="AudioStream" id=3]
[ext_resource path="res://addons/kenney_particle_pack/16/spark_05_16.png" type="Texture" id=4]

[sub_resource type="Gradient" id=58]
colors = PoolColorArray( 1, 0.820312, 0, 0.368627, 1, 1, 1, 0 )

[sub_resource type="GradientTexture2D" id=57]
gradient = SubResource( 58 )
fill = 1
fill_from = Vector2( 0.5, 0.5 )
fill_to = Vector2( 0.5, 0 )

[sub_resource type="StreamTexture" id=59]
load_path = "res://.import/pixel_meteor.png-eaaf4e780ac63500e13c7f8b3e7200c7.stex"

[sub_resource type="AtlasTexture" id=20]
atlas = SubResource( 59 )
region = Rect2( 0, 0, 64, 128 )

[sub_resource type="AtlasTexture" id=21]
atlas = SubResource( 59 )
region = Rect2( 64, 0, 64, 128 )

[sub_resource type="AtlasTexture" id=22]
atlas = SubResource( 59 )
region = Rect2( 128, 0, 64, 128 )

[sub_resource type="AtlasTexture" id=23]
atlas = SubResource( 59 )
region = Rect2( 192, 0, 64, 128 )

[sub_resource type="AtlasTexture" id=24]
atlas = SubResource( 59 )
region = Rect2( 256, 0, 64, 128 )

[sub_resource type="AtlasTexture" id=25]
atlas = SubResource( 59 )
region = Rect2( 320, 0, 64, 128 )

[sub_resource type="AtlasTexture" id=26]
atlas = SubResource( 59 )
region = Rect2( 384, 0, 64, 128 )

[sub_resource type="AtlasTexture" id=27]
atlas = SubResource( 59 )
region = Rect2( 448, 0, 64, 128 )

[sub_resource type="AtlasTexture" id=28]
atlas = SubResource( 59 )
region = Rect2( 0, 128, 64, 128 )

[sub_resource type="AtlasTexture" id=29]
atlas = SubResource( 59 )
region = Rect2( 64, 128, 64, 128 )

[sub_resource type="AtlasTexture" id=30]
atlas = SubResource( 59 )
region = Rect2( 128, 128, 64, 128 )

[sub_resource type="AtlasTexture" id=31]
atlas = SubResource( 59 )
region = Rect2( 192, 128, 64, 128 )

[sub_resource type="AtlasTexture" id=32]
atlas = SubResource( 59 )
region = Rect2( 256, 128, 64, 128 )

[sub_resource type="AtlasTexture" id=33]
atlas = SubResource( 59 )
region = Rect2( 320, 128, 64, 128 )

[sub_resource type="AtlasTexture" id=34]
atlas = SubResource( 59 )
region = Rect2( 384, 128, 64, 128 )

[sub_resource type="AtlasTexture" id=35]
atlas = SubResource( 59 )
region = Rect2( 448, 128, 64, 128 )

[sub_resource type="AtlasTexture" id=36]
atlas = SubResource( 59 )
region = Rect2( 0, 256, 64, 128 )

[sub_resource type="AtlasTexture" id=37]
atlas = SubResource( 59 )
region = Rect2( 64, 256, 64, 128 )

[sub_resource type="AtlasTexture" id=38]
atlas = SubResource( 59 )
region = Rect2( 128, 256, 64, 128 )

[sub_resource type="AtlasTexture" id=39]
atlas = SubResource( 59 )
region = Rect2( 192, 256, 64, 128 )

[sub_resource type="AtlasTexture" id=40]
atlas = SubResource( 59 )
region = Rect2( 256, 256, 64, 128 )

[sub_resource type="AtlasTexture" id=41]
atlas = SubResource( 59 )
region = Rect2( 320, 256, 64, 128 )

[sub_resource type="AtlasTexture" id=42]
atlas = SubResource( 59 )
region = Rect2( 384, 256, 64, 128 )

[sub_resource type="AtlasTexture" id=43]
atlas = SubResource( 59 )
region = Rect2( 448, 256, 64, 128 )

[sub_resource type="AtlasTexture" id=44]
atlas = SubResource( 59 )
region = Rect2( 0, 384, 64, 128 )

[sub_resource type="AtlasTexture" id=45]
atlas = SubResource( 59 )
region = Rect2( 64, 384, 64, 128 )

[sub_resource type="AtlasTexture" id=46]
atlas = SubResource( 59 )
region = Rect2( 128, 384, 64, 128 )

[sub_resource type="AtlasTexture" id=47]
atlas = SubResource( 59 )
region = Rect2( 192, 384, 64, 128 )

[sub_resource type="AtlasTexture" id=48]
atlas = SubResource( 59 )
region = Rect2( 256, 384, 64, 128 )

[sub_resource type="AtlasTexture" id=49]
atlas = SubResource( 59 )
region = Rect2( 320, 384, 64, 128 )

[sub_resource type="AtlasTexture" id=50]
atlas = SubResource( 59 )
region = Rect2( 384, 384, 64, 128 )

[sub_resource type="AtlasTexture" id=51]
atlas = SubResource( 59 )
region = Rect2( 448, 384, 64, 128 )

[sub_resource type="SpriteFrames" id=6]
animations = [ {
"frames": [ SubResource( 20 ), SubResource( 21 ), SubResource( 22 ), SubResource( 23 ), SubResource( 24 ), SubResource( 25 ), SubResource( 26 ), SubResource( 27 ), SubResource( 28 ), SubResource( 29 ), SubResource( 30 ), SubResource( 31 ), SubResource( 32 ), SubResource( 33 ), SubResource( 34 ), SubResource( 35 ), SubResource( 36 ), SubResource( 37 ), SubResource( 38 ), SubResource( 39 ), SubResource( 40 ), SubResource( 41 ), SubResource( 42 ), SubResource( 43 ), SubResource( 44 ), SubResource( 45 ), SubResource( 46 ), SubResource( 47 ), SubResource( 48 ), SubResource( 49 ), SubResource( 50 ), SubResource( 51 ) ],
"loop": true,
"name": "default",
"speed": 25.0
} ]

[sub_resource type="CircleShape2D" id=19]
radius = 13.0

[sub_resource type="Gradient" id=52]
offsets = PoolRealArray( 0.002849, 0.386609, 0.666667 )
colors = PoolColorArray( 0, 0.996078, 0.623529, 0.92549, 0, 0.976471, 1, 0.513726, 0, 1, 0.227451, 0 )

[sub_resource type="GradientTexture" id=56]
gradient = SubResource( 52 )

[sub_resource type="ParticlesMaterial" id=55]
emission_shape = 1
emission_sphere_radius = 10.65
flag_align_y = true
flag_rotate_y = true
flag_disable_z = true
direction = Vector3( 0, 0, 0 )
spread = 0.0
gravity = Vector3( 0, 0, 0 )
orbit_velocity = 0.0
orbit_velocity_random = 0.0
radial_accel = 100.0
radial_accel_random = 1.0
scale = 1.2
scale_random = 0.5
color_ramp = SubResource( 56 )

[node name="EnemyBullet_2" type="Area2D"]
collision_layer = 8
collision_mask = 33
script = ExtResource( 2 )

[node name="Sprite" type="Sprite" parent="."]
scale = Vector2( 0.296875, 0.453125 )
texture = SubResource( 57 )

[node name="AnimatedSprite" type="AnimatedSprite" parent="."]
modulate = Color( 1, 1, 1, 0.615686 )
position = Vector2( 0, 16 )
rotation = 3.14159
scale = Vector2( 0.25, 0.4 )
frames = SubResource( 6 )
playing = true

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
visible = false
shape = SubResource( 19 )

[node name="Particles2D" type="Particles2D" parent="."]
amount = 15
lifetime = 0.5
local_coords = false
process_material = SubResource( 55 )
texture = ExtResource( 4 )

[node name="AudioStreamPlayer2D" type="AudioStreamPlayer2D" parent="."]
stream = ExtResource( 3 )
volume_db = -10.0
