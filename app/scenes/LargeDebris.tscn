[gd_scene load_steps=15 format=2]

[ext_resource path="res://assets/Debries1.png" type="Texture" id=1]
[ext_resource path="res://assets/Debries4.png" type="Texture" id=2]
[ext_resource path="res://assets/Debries3.png" type="Texture" id=3]
[ext_resource path="res://assets/Debries2.png" type="Texture" id=4]
[ext_resource path="res://assets/Debries7.png" type="Texture" id=5]
[ext_resource path="res://assets/Debries5.png" type="Texture" id=6]
[ext_resource path="res://assets/Debries6.png" type="Texture" id=7]
[ext_resource path="res://scripts/DebrisBase.gd" type="Script" id=8]
[ext_resource path="res://addons/kenney_particle_pack/16/dirt_03_16.png" type="Texture" id=9]

[sub_resource type="CircleShape2D" id=1]
radius = 20.025

[sub_resource type="Gradient" id=32]
offsets = PoolRealArray( 0, 0.273551, 0.570652, 1 )
colors = PoolColorArray( 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 0.933333, 1, 1, 1, 0 )

[sub_resource type="GradientTexture" id=31]
gradient = SubResource( 32 )

[sub_resource type="ParticlesMaterial" id=33]
emission_shape = 1
emission_sphere_radius = 15.0
flag_disable_z = true
direction = Vector3( 0, -1, 0 )
spread = 0.0
gravity = Vector3( 0, 0, 0 )
initial_velocity = 10.0
angular_velocity = 100.0
angular_velocity_random = 1.0
orbit_velocity = 0.0
orbit_velocity_random = 0.0
linear_accel = 10.0
radial_accel_random = 1.0
damping = 10.0
damping_random = 1.0
scale = 2.0
scale_random = 1.0
color_ramp = SubResource( 31 )
hue_variation = 0.22
hue_variation_random = 0.48

[sub_resource type="SpriteFrames" id=2]
animations = [ {
"frames": [ ExtResource( 1 ), ExtResource( 4 ), ExtResource( 3 ), ExtResource( 2 ), ExtResource( 6 ), ExtResource( 7 ), ExtResource( 5 ) ],
"loop": true,
"name": "default",
"speed": 5.0
} ]

[node name="LargeDebris" type="Area2D"]
collision_layer = 32
collision_mask = 3
script = ExtResource( 8 )

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource( 1 )

[node name="Particles2D" type="Particles2D" parent="."]
modulate = Color( 1, 1, 1, 0.313726 )
amount = 30
local_coords = false
process_material = SubResource( 33 )
texture = ExtResource( 9 )

[node name="Sprite" type="AnimatedSprite" parent="."]
scale = Vector2( 2, 2 )
frames = SubResource( 2 )
