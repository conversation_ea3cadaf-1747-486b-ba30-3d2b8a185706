[gd_scene load_steps=20 format=2]

[ext_resource path="res://assets/1_asteroid_sprites.png" type="Texture" id=1]
[ext_resource path="res://scripts/EnemyBullet_2.gd" type="Script" id=2]
[ext_resource path="res://assets/sounds/laser1.wav" type="AudioStream" id=3]

[sub_resource type="Gradient" id=22]
colors = PoolColorArray( 0.172549, 1, 0, 0.368627, 1, 1, 1, 0 )

[sub_resource type="GradientTexture2D" id=20]
gradient = SubResource( 22 )
fill = 1
fill_from = Vector2( 0.5, 0.5 )
fill_to = Vector2( 0.5, 0 )

[sub_resource type="AtlasTexture" id=7]
atlas = ExtResource( 1 )
region = Rect2( 0, 0, 128, 128 )

[sub_resource type="AtlasTexture" id=21]
atlas = ExtResource( 1 )
region = Rect2( 128, 0, 128, 128 )

[sub_resource type="AtlasTexture" id=9]
atlas = ExtResource( 1 )
region = Rect2( 256, 0, 128, 128 )

[sub_resource type="AtlasTexture" id=10]
atlas = ExtResource( 1 )
region = Rect2( 384, 0, 128, 128 )

[sub_resource type="AtlasTexture" id=11]
atlas = ExtResource( 1 )
region = Rect2( 512, 0, 128, 128 )

[sub_resource type="AtlasTexture" id=12]
atlas = ExtResource( 1 )
region = Rect2( 0, 128, 128, 128 )

[sub_resource type="AtlasTexture" id=13]
atlas = ExtResource( 1 )
region = Rect2( 128, 128, 128, 128 )

[sub_resource type="AtlasTexture" id=14]
atlas = ExtResource( 1 )
region = Rect2( 256, 128, 128, 128 )

[sub_resource type="AtlasTexture" id=15]
atlas = ExtResource( 1 )
region = Rect2( 384, 128, 128, 128 )

[sub_resource type="AtlasTexture" id=16]
atlas = ExtResource( 1 )
region = Rect2( 512, 128, 128, 128 )

[sub_resource type="AtlasTexture" id=17]
atlas = ExtResource( 1 )
region = Rect2( 0, 256, 128, 128 )

[sub_resource type="AtlasTexture" id=18]
atlas = ExtResource( 1 )
region = Rect2( 128, 256, 128, 128 )

[sub_resource type="SpriteFrames" id=6]
animations = [ {
"frames": [ SubResource( 7 ), SubResource( 21 ), SubResource( 9 ), SubResource( 10 ), SubResource( 11 ), SubResource( 12 ), SubResource( 13 ), SubResource( 14 ), SubResource( 15 ), SubResource( 16 ), SubResource( 17 ), SubResource( 18 ) ],
"loop": true,
"name": "default",
"speed": 40.0
} ]

[sub_resource type="CircleShape2D" id=19]
radius = 13.0

[node name="EnemyBullet_2" type="Area2D"]
collision_layer = 8
collision_mask = 33
script = ExtResource( 2 )

[node name="Sprite" type="Sprite" parent="."]
scale = Vector2( 0.296875, 0.453125 )
texture = SubResource( 20 )

[node name="AnimatedSprite" type="AnimatedSprite" parent="."]
modulate = Color( 0.968627, 1, 0, 1 )
position = Vector2( 9.53674e-07, 3 )
rotation = 3.14159
scale = Vector2( 0.204801, 0.659384 )
frames = SubResource( 6 )
playing = true

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
visible = false
shape = SubResource( 19 )

[node name="AudioStreamPlayer2D" type="AudioStreamPlayer2D" parent="."]
stream = ExtResource( 3 )
volume_db = -10.0
