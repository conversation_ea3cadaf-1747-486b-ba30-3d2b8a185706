[gd_scene load_steps=31 format=2]

[ext_resource path="res://scenes/Themes/tryu_theme.tres" type="Theme" id=1]
[ext_resource path="res://scripts/Pause.gd" type="Script" id=2]
[ext_resource path="res://scripts/Game.gd" type="Script" id=3]
[ext_resource path="res://scenes/Sidebar.tscn" type="PackedScene" id=4]
[ext_resource path="res://scenes/levels/LevelConductor.tscn" type="PackedScene" id=5]
[ext_resource path="res://scripts/GameCamera2D.gd" type="Script" id=6]
[ext_resource path="res://assets/fonts/c64.tres" type="DynamicFont" id=7]
[ext_resource path="res://plugins/touchJoyPad/touchJoyPad.tscn" type="PackedScene" id=8]
[ext_resource path="res://plugins/touchJoyPad/analog/big_circle.png" type="Texture" id=9]
[ext_resource path="res://plugins/touchJoyPad/analog/big_circle_pressed.png" type="Texture" id=10]
[ext_resource path="res://scenes/Themes/in_game_terminal_font.tres" type="DynamicFont" id=11]
[ext_resource path="res://assets/bg_images/blue_nebula_bg.png" type="Texture" id=12]
[ext_resource path="res://assets/bg_images/planets_bg.jpg" type="Texture" id=13]
[ext_resource path="res://scripts/GameBackground.gd" type="Script" id=14]
[ext_resource path="res://assets/bg_images/nebula_bg_orange.jpg" type="Texture" id=15]
[ext_resource path="res://assets/bg_images/nebula_bg.jpg" type="Texture" id=16]
[ext_resource path="res://scenes/Themes/in_game_message_font.tres" type="DynamicFont" id=17]
[ext_resource path="res://scenes/Themes/in_game_ui_font.tres" type="DynamicFont" id=18]
[ext_resource path="res://scenes/Themes/ok_cancel_dlg.tres" type="Theme" id=19]

[sub_resource type="Shader" id=11]
code = "shader_type canvas_item;
const float UPDATE_INTERVAL = .163;
const float STATIC_GRANULARITY = .01;
const float EDGE_BLUR = .5;
const float BORDER_SIZE = .3;

float generate_random_static (in float size, in float interval, in vec2 uv){
	float time_step = TIME - mod(TIME,interval);
	vec2 uv_step = uv - mod(uv, size);
	return fract(sin(dot(uv_step,vec2(12.0278*sin(time_step),15.0905)))*43758.5453);
}

vec2 get_polar_coords (vec2 center, vec2 uv){
	vec2 pos = uv-center;
	float r = length(pos);
	float theta = atan(pos.y,pos.x);
	return vec2(r,theta);
}

vec4 layer (in vec4 front_color, in vec4 back_color){
	return vec4(mix(back_color.rgb,front_color.rgb,front_color.a),front_color.a+back_color.a);
}

void fragment() {
	vec3 static_plot = vec3(generate_random_static(STATIC_GRANULARITY,UPDATE_INTERVAL,UV));
	
	vec2 c1 = vec2(0.5);
	vec2 pv1 = get_polar_coords(c1,UV);
	float func = BORDER_SIZE-.015*cos(4.0*pv1.y);
	float border_plot = smoothstep(func,func+EDGE_BLUR, pv1.x);
	vec4 border_color = vec4(vec3(0.0),1.0)*border_plot;
	COLOR = vec4(static_plot,.1);
	COLOR = layer(COLOR,border_color);
}"

[sub_resource type="ShaderMaterial" id=9]
shader = SubResource( 11 )

[sub_resource type="ProxyTexture" id=10]

[sub_resource type="Environment" id=7]
background_mode = 4
glow_enabled = true
glow_bloom = 0.1
glow_blend_mode = 1
glow_bicubic_upscale = true
glow_high_quality = true
adjustment_enabled = true

[sub_resource type="ParticlesMaterial" id=3]
lifetime_randomness = 0.5
emission_shape = 2
emission_box_extents = Vector3( 450, 1, 1 )
flag_disable_z = true
direction = Vector3( 0, 1, 0 )
spread = 0.0
gravity = Vector3( 0, 0, 0 )
initial_velocity = 50.0
orbit_velocity = 0.0
orbit_velocity_random = 0.0
scale = 0.5
scale_random = 0.5
color = Color( 1, 1, 1, 0.490196 )
hue_variation = 0.05
hue_variation_random = 0.2

[sub_resource type="ParticlesMaterial" id=1]
lifetime_randomness = 0.5
emission_shape = 2
emission_box_extents = Vector3( 450, 1, 1 )
flag_disable_z = true
direction = Vector3( 0, 1, 0 )
spread = 0.0
gravity = Vector3( 0, 0, 0 )
initial_velocity = 75.0
orbit_velocity = 0.0
orbit_velocity_random = 0.0
scale = 0.5
scale_random = 0.5
color = Color( 1, 1, 1, 0.490196 )
hue_variation = -0.05
hue_variation_random = 0.2

[sub_resource type="ParticlesMaterial" id=2]
lifetime_randomness = 0.5
emission_shape = 2
emission_box_extents = Vector3( 450, 1, 1 )
flag_disable_z = true
direction = Vector3( 0, 1, 0 )
spread = 0.0
gravity = Vector3( 0, 0, 0 )
initial_velocity = 100.0
orbit_velocity = 0.0
orbit_velocity_random = 0.0
color = Color( 1, 1, 1, 0.745098 )
hue_variation = 1.0

[sub_resource type="Gradient" id=4]
offsets = PoolRealArray( 0, 0.269129, 0.707124 )
colors = PoolColorArray( 0, 0, 0, 0, 0, 0, 0, 0.481343, 0, 0, 0, 1 )

[sub_resource type="GradientTexture2D" id=5]
gradient = SubResource( 4 )
width = 1024
fill_from = Vector2( 0.5125, 0 )
fill_to = Vector2( 0.513636, 1 )

[sub_resource type="StyleBoxFlat" id=12]
bg_color = Color( 0, 0, 0, 0.54902 )

[sub_resource type="Theme" id=13]
Label/styles/normal = SubResource( 12 )

[node name="Main" type="Node2D"]
__meta__ = {
"_edit_horizontal_guides_": [ 476.0 ]
}

[node name="GameBackground" type="Node2D" parent="."]
script = ExtResource( 14 )

[node name="Sprite1" type="Sprite" parent="GameBackground"]
modulate = Color( 1, 1, 1, 0.117647 )
position = Vector2( 512, 0 )
texture = ExtResource( 16 )
offset = Vector2( -32, 748 )

[node name="Sprite_1" type="Sprite" parent="GameBackground"]
visible = false
modulate = Color( 1, 1, 1, 0.105882 )
position = Vector2( 512, 0 )
texture = ExtResource( 16 )
offset = Vector2( -32, 748 )

[node name="Sprite_2" type="Sprite" parent="GameBackground"]
visible = false
modulate = Color( 1, 1, 1, 0.152941 )
position = Vector2( 512, 0 )
texture = ExtResource( 13 )
offset = Vector2( -32, 748 )

[node name="Sprite_3" type="Sprite" parent="GameBackground"]
visible = false
modulate = Color( 1, 1, 1, 0.254902 )
position = Vector2( 512, 0 )
texture = ExtResource( 12 )
offset = Vector2( -32, 748 )

[node name="Sprite_4" type="Sprite" parent="GameBackground"]
visible = false
modulate = Color( 1, 1, 1, 0.137255 )
position = Vector2( 512, 0 )
texture = ExtResource( 15 )
offset = Vector2( -32, 748 )

[node name="Game" type="Node" parent="."]
script = ExtResource( 3 )

[node name="Camera2D" type="Camera2D" parent="Game"]
position = Vector2( 512, 289 )
rotating = true
current = true
smoothing_enabled = true
smoothing_speed = 100.0
script = ExtResource( 6 )
max_offset = Vector2( 50, 30 )

[node name="TVFrame" type="Sprite" parent="Game"]
visible = false
material = SubResource( 9 )
position = Vector2( 512, 288 )
scale = Vector2( 1024, 576 )
texture = SubResource( 10 )

[node name="WorldEnvironment" type="WorldEnvironment" parent="Game"]
environment = SubResource( 7 )

[node name="CurrentLevel" type="Node2D" parent="Game"]

[node name="LevelConductor" parent="Game" instance=ExtResource( 5 )]

[node name="BG" type="Node" parent="Game"]

[node name="BGStarsSlow" type="Particles2D" parent="Game/BG"]
position = Vector2( 512, 0 )
z_index = 1
amount = 100
lifetime = 15.0
process_material = SubResource( 3 )

[node name="BGStars" type="Particles2D" parent="Game/BG"]
position = Vector2( 512, 0 )
z_index = 1
amount = 60
lifetime = 10.0
process_material = SubResource( 1 )

[node name="BGStarsFast" type="Particles2D" parent="Game/BG"]
position = Vector2( 512, 0 )
z_index = 1
amount = 30
lifetime = 10.0
process_material = SubResource( 2 )

[node name="BottomFader" type="Sprite" parent="Game/BG"]
position = Vector2( 512, 568 )
z_index = 2
texture = SubResource( 5 )
region_rect = Rect2( 0, 0, 1024, 50 )

[node name="Sidebars" parent="Game/BG" instance=ExtResource( 4 )]
modulate = Color( 0, 1, 0.670588, 0.682353 )
scale = Vector2( 1, 0.995403 )
z_index = 4096

[node name="BackgroundMusicPlayer" type="AudioStreamPlayer" parent="Game"]
volume_db = -15.0

[node name="Overlay" type="Node2D" parent="Game"]

[node name="DemoCountdown" type="Label" parent="Game/Overlay"]
visible = false
modulate = Color( 1, 1, 1, 0.32549 )
margin_left = 4.0
margin_top = 541.0
margin_right = 929.0
margin_bottom = 566.0
rect_scale = Vector2( 1.10042, 1.26466 )
custom_fonts/font = ExtResource( 18 )
text = "DEMO ENDS IN 0:00"
align = 1
valign = 1

[node name="LevelLabel" type="Label" parent="Game/Overlay"]
modulate = Color( 1, 1, 1, 0.27451 )
margin_left = 16.0
margin_top = 80.0
margin_right = 120.0
margin_bottom = 117.0
custom_fonts/font = ExtResource( 18 )
text = "Level
2"
valign = 1

[node name="EffectsLabel" type="Label" parent="Game/Overlay"]
modulate = Color( 1, 1, 1, 0.27451 )
margin_left = 16.0
margin_top = 144.0
margin_right = 120.0
margin_bottom = 556.0
custom_fonts/font = ExtResource( 7 )

[node name="LifeLabel" type="Label" parent="Game/Overlay"]
modulate = Color( 1, 1, 1, 0.27451 )
margin_left = 16.0
margin_top = 16.0
margin_right = 120.0
margin_bottom = 40.0
custom_fonts/font = ExtResource( 18 )
text = "Ships
2"
valign = 1

[node name="ScoreLabel" type="Label" parent="Game/Overlay"]
modulate = Color( 1, 1, 1, 0.27451 )
margin_left = 880.0
margin_top = 16.0
margin_right = 1008.0
margin_bottom = 40.0
custom_fonts/font = ExtResource( 18 )
text = "0"
align = 2
valign = 1

[node name="MoneyLabel" type="Label" parent="Game/Overlay"]
modulate = Color( 1, 1, 1, 0.27451 )
margin_left = 880.0
margin_top = 80.0
margin_right = 1008.0
margin_bottom = 104.0
custom_fonts/font = ExtResource( 18 )
text = "$0"
align = 2
valign = 1

[node name="TerminalLabel" type="Label" parent="Game/Overlay"]
modulate = Color( 0.423529, 1, 0.329412, 1 )
margin_left = 512.0
margin_top = 129.0
margin_right = 1008.0
margin_bottom = 384.0
grow_horizontal = 0
grow_vertical = 0
custom_fonts/font = ExtResource( 11 )
text = "TERMINAL TEXT"
align = 2
valign = 2

[node name="VoidScore" type="Label" parent="Game/Overlay"]
visible = false
modulate = Color( 1, 0, 0, 0.317647 )
margin_left = 840.0
margin_top = 16.0
margin_right = 1008.0
margin_bottom = 40.0
rect_rotation = -4.0
custom_fonts/font = ExtResource( 7 )
text = "_________"
align = 2
valign = 1

[node name="VoidScore2" type="Label" parent="Game/Overlay/VoidScore"]
modulate = Color( 1, 0, 0, 1 )
margin_left = 3.96924
margin_top = -13.7566
margin_right = 171.969
margin_bottom = 10.2434
rect_rotation = 8.0
custom_fonts/font = ExtResource( 7 )
text = "_________"
align = 2
valign = 1

[node name="VoidMoney" type="Label" parent="Game/Overlay"]
visible = false
modulate = Color( 1, 0, 0, 0.317647 )
margin_left = 840.0
margin_top = 80.0
margin_right = 1009.0
margin_bottom = 104.0
rect_rotation = -4.0
custom_fonts/font = ExtResource( 7 )
text = "_________"
align = 2
valign = 1

[node name="VoidMoney2" type="Label" parent="Game/Overlay/VoidMoney"]
modulate = Color( 1, 0, 0, 1 )
margin_left = 3.96924
margin_top = -13.7566
margin_right = 172.969
margin_bottom = 10.2434
rect_rotation = 8.0
custom_fonts/font = ExtResource( 7 )
text = "_________"
align = 2
valign = 1

[node name="LevelTitle" type="CanvasLayer" parent="Game"]

[node name="MessageLabel" type="Label" parent="Game/LevelTitle"]
margin_top = 193.0
margin_right = 410.0
margin_bottom = 225.0
rect_scale = Vector2( 2.5, 2.5 )
custom_fonts/font = ExtResource( 17 )
text = "Ready?"
align = 1
valign = 1
clip_text = true

[node name="Pause" type="CanvasLayer" parent="Game"]
pause_mode = 2
visible = false
script = ExtResource( 2 )

[node name="Panel" type="Panel" parent="Game/Pause"]
modulate = Color( 0, 0, 0, 0.87451 )
margin_right = 1024.0
margin_bottom = 600.0

[node name="Label" type="Label" parent="Game/Pause"]
margin_top = -1.0
margin_right = 256.0
margin_bottom = 149.0
rect_scale = Vector2( 4, 4 )
theme = SubResource( 13 )
custom_fonts/font = ExtResource( 17 )
text = "Paused"
align = 1
valign = 1

[node name="PauseButton" type="Button" parent="Game/Pause"]
anchor_left = 1.0
anchor_right = 1.0
margin_left = -616.0
margin_top = 352.0
margin_right = -408.0
margin_bottom = 402.0
focus_neighbour_top = NodePath("../QuitButton")
focus_neighbour_bottom = NodePath("../QuitButton")
theme = ExtResource( 1 )
text = "Contine"

[node name="QuitButton" type="Button" parent="Game/Pause"]
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
margin_left = -104.0
margin_top = -168.0
margin_right = 107.0
margin_bottom = -118.0
focus_neighbour_top = NodePath("../PauseButton")
focus_neighbour_bottom = NodePath("../PauseButton")
theme = ExtResource( 1 )
text = "Main Menu"

[node name="QuitDialog" type="ConfirmationDialog" parent="Game/Pause"]
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
margin_left = -191.0
margin_top = -45.0
margin_right = 158.0
margin_bottom = 154.0
rect_min_size = Vector2( 300, 105 )
rect_scale = Vector2( 1.1, 1.1 )
theme = ExtResource( 19 )
window_title = "Are you sure?"

[node name="PauseButton" type="Button" parent="Game"]
anchor_left = 1.0
anchor_right = 1.0
margin_left = -272.0
margin_top = 16.0
margin_right = -194.0
margin_bottom = 66.0
rect_scale = Vector2( 0.7, 0.7 )
theme = ExtResource( 1 )
text = "||"

[node name="Controller" type="Node2D" parent="."]

[node name="GamePad" parent="Controller" instance=ExtResource( 8 )]
position = Vector2( 120, 476 )
leftPadStyle = "JoyStick"

[node name="TouchScreenButton" type="TouchScreenButton" parent="Controller"]
position = Vector2( 818, 396 )
normal = ExtResource( 9 )
pressed = ExtResource( 10 )
action = "button_fire"
